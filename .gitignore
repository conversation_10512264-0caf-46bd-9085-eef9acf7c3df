# Dependency directories
node_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Coverage report
.nyc_output/

# Optional eslint cache
.eslintcache

# dotenv environment variable files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Build output
dist/
build/
out/

# IDE and editor folders
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Credentials and sensitive information
*.pem
*.key