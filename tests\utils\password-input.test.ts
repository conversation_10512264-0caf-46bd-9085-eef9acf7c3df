/**
 * Comprehensive tests for password input utility
 */

import { askPassword, askPasswordWithConfirmation } from '../../src/utils/password-input';

// Mock process.stdin and process.stdout for testing
const mockStdin = {
  isTTY: true,
  isRaw: false,
  setRawMode: jest.fn(),
  resume: jest.fn(),
  pause: jest.fn(),
  setEncoding: jest.fn(),
  on: jest.fn(),
  removeListener: jest.fn(),
  listeners: jest.fn(() => []),
  emit: jest.fn()
};

const mockStdout = {
  write: jest.fn()
};

// Store original stdin/stdout
const originalStdin = process.stdin;
const originalStdout = process.stdout;

describe('Password Input Utility', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock stdin and stdout
    Object.defineProperty(process, 'stdin', {
      value: mockStdin,
      writable: true
    });
    Object.defineProperty(process, 'stdout', {
      value: mockStdout,
      writable: true
    });

    // Reset mock state
    mockStdin.isTTY = true;
    mockStdin.isRaw = false;
    mockStdin.setRawMode.mockReturnValue(mockStdin);
  });

  afterEach(() => {
    // Restore original stdin/stdout
    Object.defineProperty(process, 'stdin', {
      value: originalStdin,
      writable: true
    });
    Object.defineProperty(process, 'stdout', {
      value: originalStdout,
      writable: true
    });
  });

  describe('askPassword', () => {
    describe('TTY Mode (Interactive Terminal)', () => {
      beforeEach(() => {
        mockStdin.isTTY = true;
      });

      it('should prompt for password and return entered text', async () => {
        const prompt = 'Enter password: ';
        const expectedPassword = 'secret123';

        // Mock the data event to simulate user input
        mockStdin.on.mockImplementation((event, callback) => {
          if (event === 'data') {
            // Simulate typing password followed by Enter
            setTimeout(() => {
              // Simulate each character being typed
              for (const char of expectedPassword) {
                callback(char);
              }
              // Simulate Enter key (charCode 13)
              callback('\r');
            }, 10);
          }
        });

        const passwordPromise = askPassword(prompt);
        const result = await passwordPromise;

        expect(result).toBe(expectedPassword);
        expect(mockStdout.write).toHaveBeenCalledWith(prompt);
        expect(mockStdin.setRawMode).toHaveBeenCalledWith(true);
        expect(mockStdin.resume).toHaveBeenCalled();
        expect(mockStdin.setEncoding).toHaveBeenCalledWith('utf8');
      });

      it('should mask password input with asterisks', async () => {
        const prompt = 'Password: ';
        const password = 'test';

        mockStdin.on.mockImplementation((event, callback) => {
          if (event === 'data') {
            setTimeout(() => {
              // Simulate typing each character
              for (const char of password) {
                callback(char);
              }
              callback('\r'); // Enter
            }, 10);
          }
        });

        await askPassword(prompt);

        // Should write backspace and asterisk for each character
        const writeCalls = mockStdout.write.mock.calls;
        expect(writeCalls[0][0]).toBe(prompt); // Initial prompt

        // Each character should result in backspace + asterisk
        let asteriskCount = 0;
        for (let i = 1; i < writeCalls.length; i++) {
          if (writeCalls[i][0] === '*') {
            asteriskCount++;
          }
        }
        expect(asteriskCount).toBe(password.length);
      });

      it('should handle backspace correctly', async () => {
        const prompt = 'Password: ';

        mockStdin.on.mockImplementation((event, callback) => {
          if (event === 'data') {
            setTimeout(() => {
              callback('a'); // Type 'a'
              callback('b'); // Type 'b'
              callback('\x7f'); // Backspace (charCode 127)
              callback('c'); // Type 'c'
              callback('\r'); // Enter
            }, 10);
          }
        });

        const result = await askPassword(prompt);
        expect(result).toBe('ac'); // 'b' should be removed by backspace
      });

      it('should handle Ctrl+C gracefully', async () => {
        const prompt = 'Password: ';

        mockStdin.on.mockImplementation((event, callback) => {
          if (event === 'data') {
            setTimeout(() => {
              callback('\x03'); // Ctrl+C (charCode 3)
            }, 10);
          }
        });

        const result = await askPassword(prompt);
        expect(result).toBe(''); // Should return empty string on Ctrl+C
        expect(mockStdout.write).toHaveBeenCalledWith('^C\n');
      });
    });

    describe('Non-TTY Mode (Piped Input)', () => {
      beforeEach(() => {
        mockStdin.isTTY = false;
      });

      it('should handle piped input correctly', async () => {
        const prompt = 'Password: ';
        const pipedPassword = 'piped-password\n';

        mockStdin.on.mockImplementation((event, callback) => {
          if (event === 'data') {
            setTimeout(() => {
              callback(pipedPassword);
            }, 10);
          }
        });

        const result = await askPassword(prompt);
        expect(result).toBe('piped-password'); // Newline should be stripped
        expect(mockStdout.write).toHaveBeenCalledWith(prompt);
        expect(mockStdin.setRawMode).not.toHaveBeenCalled(); // Raw mode not used in non-TTY
      });
    });
  });

  describe('askPasswordWithConfirmation', () => {
    it('should return password when both entries match', async () => {
      const password = 'matching-password';
      let callCount = 0;

      mockStdin.on.mockImplementation((event, callback) => {
        if (event === 'data') {
          setTimeout(() => {
            for (const char of password) {
              callback(char);
            }
            callback('\r');
            callCount++;
          }, 10);
        }
      });

      const result = await askPasswordWithConfirmation('Enter password: ');
      expect(result).toBe(password);
    });

    it('should throw error when passwords do not match', async () => {
      let callCount = 0;

      mockStdin.on.mockImplementation((event, callback) => {
        if (event === 'data') {
          setTimeout(() => {
            if (callCount === 0) {
              // First password
              callback('password1\r');
            } else {
              // Second password (different)
              callback('password2\r');
            }
            callCount++;
          }, 10);
        }
      });

      await expect(askPasswordWithConfirmation('Enter password: '))
        .rejects.toThrow('Passwords do not match');
    });
  });
});