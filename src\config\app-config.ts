/**
 * Centralized application configuration and constants
 */

import * as path from 'path';
import * as os from 'os';

export interface AppPaths {
  configDir: string;
  keyFile: string;
  credentialsFile: string;
  cookiesFile: string;
}

export interface CryptoConfig {
  keyLength: number;
  pbkdf2Iterations: {
    masterKey: number;
    data: number;
  };
  algorithm: string;
  hashAlgorithm: string;
  ivLength: number;
  saltLength: {
    masterKey: number;
    data: number;
  };
}

export interface RetryConfig {
  maxAttempts: number;
  timeoutMs: number;
  backoffMs: number;
}

export interface HttpConfig {
  userAgent: string;
  timeout: number;
}

export interface AppConfig {
  paths: AppPaths;
  crypto: CryptoConfig;
  retry: RetryConfig;
  http: HttpConfig;
  defaultLogLevel: 'info' | 'debug' | 'verbose' | 'warn' | 'error' | 'silent';
}

// Default configuration
const secureConfigDir = path.join(os.homedir(), '.confluence-uploader');

export const defaultConfig: AppConfig = {
  paths: {
    configDir: secureConfigDir,
    keyFile: path.join(secureConfigDir, '.key'),
    credentialsFile: path.join(secureConfigDir, 'credentials.enc'),
    cookiesFile: path.join(secureConfigDir, 'cookies.enc')
  },
  crypto: {
    keyLength: 32, // 256 bits
    pbkdf2Iterations: {
      masterKey: 100000,
      data: 10000
    },
    algorithm: 'aes-256-gcm',
    hashAlgorithm: 'sha256',
    ivLength: 16, // 128 bits
    saltLength: {
      masterKey: 32, // 256 bits
      data: 16 // 128 bits
    }
  },
  retry: {
    maxAttempts: 3,
    timeoutMs: 30000, // 30 seconds
    backoffMs: 1000 // 1 second
  },
  http: {
    userAgent: 'confluence-upload/1.0.0 (Node.js; Confluence Page Updater)',
    timeout: 30000 // 30 seconds
  },
  defaultLogLevel: 'info'
};

// Export individual configs for convenience
export const paths = defaultConfig.paths;
export const cryptoConfig = defaultConfig.crypto;
export const retryConfig = defaultConfig.retry;