/**
 * Shared type definitions for the Confluence page update project
 */

export interface ConfluenceCredential {
  readonly id?: string; // Add unique identifier for future use
  name: string;
  baseUrl: string;
  spaceKey: string;
  authMethod?: 'token' | 'browser'; // Add auth method distinction
  token?: string;
  username?: string;
  password?: string;
  puppeteerLogin: boolean;
  createdAt?: number;
  updatedAt?: number;
}

export interface EnhancedCookie {
  name: string;
  value: string;
  domain: string;
  path?: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
  savedAt: number;
  baseUrl: string;
}

export interface CookieStorage {
  cookies: EnhancedCookie[];
  version: string;
}

export interface EncryptedStorage {
  salt: string;
  iv: string;
  encryptedData: string;
  version?: string;
}

export interface EncryptedCookieStorage extends EncryptedStorage {
  encrypted: true;
}

// Re-export crypto types for convenience
export type { EncryptedMasterKey, EncryptedData } from '../utils/crypto-utils';
export type { LogLevel, LoggerConfig } from '../utils/logger';