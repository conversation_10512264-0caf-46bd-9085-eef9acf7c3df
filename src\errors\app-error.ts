/**
 * Base error class for application-specific errors
 * Provides consistent error handling and user-friendly messages
 */
export abstract class AppError extends Error {
  /**
   * Unique error code for identification and logging
   */
  abstract readonly code: string;
  
  /**
   * User-friendly message that can be displayed to the user
   */
  abstract readonly userMessage: string;
  
  /**
   * Process exit code to use when this error causes termination
   */
  abstract readonly exitCode: number;
  
  /**
   * Technical details for logging (not shown to users by default)
   */
  readonly details?: Record<string, any>;
  
  constructor(message: string, details?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    this.details = details;
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype);
  }
  
  /**
   * Returns a JSON representation of the error for logging
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      userMessage: this.userMessage,
      exitCode: this.exitCode,
      details: this.details
    };
  }
}