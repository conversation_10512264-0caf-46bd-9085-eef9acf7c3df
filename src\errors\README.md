# Error Handling System

This directory contains a standardized error handling system for the Confluence Page Update project. The system provides consistent error types, user-friendly messages, and a unified approach to error handling throughout the application.

## Key Components

### Base Error Class

`AppError` is the abstract base class for all application-specific errors. It provides:
- A unique error code for identification
- A user-friendly message for display
- An exit code for process termination
- Optional technical details for logging

### Specific Error Types

The system includes several specific error types:

- **AuthenticationError**: For authentication and authorization failures
- **FileOperationError**: For file system operation failures
- **NetworkError**: For network and API request failures
- **ValidationError**: For input validation failures
- **CryptoError**: For encryption/decryption failures
- **ApplicationError**: For general application errors

### Error Handling Wrapper

The `withErrorHandling` function provides a consistent way to handle errors:
- Catches and processes both AppError instances and generic errors
- Logs appropriate messages based on error type
- Supports custom error handlers
- Can automatically exit the process with the appropriate exit code

## Usage Examples

### Throwing Typed Errors

```typescript
// Instead of:
throw new Error('Invalid password');

// Use:
throw AuthenticationError.invalidPassword();
```

### Using Factory Methods

```typescript
// For file not found:
throw FileOperationError.notFound('/path/to/file');

// For network errors:
throw NetworkError.httpError('https://api.example.com', 404);

// For validation errors:
throw ValidationError.missingRequired('username');
```

### Using the Error Handling Wrapper

```typescript
async function authenticateUser(username: string, password: string): Promise<void> {
  return withErrorHandling(async () => {
    // Authentication logic here
    if (!isValidPassword(password)) {
      throw AuthenticationError.invalidPassword();
    }
    
    // Success path continues here
  }, {
    context: 'user authentication',
    exitOnError: true
  });
}
```

## Error Codes and Exit Codes

- **Authentication errors**: Exit code 1
- **Validation errors**: Exit code 1
- **File operation errors**: Exit code 2
- **Network errors**: Exit code 2
- **Crypto errors**: Exit code 3
- **Application errors**: Exit code 3

## Testing

Run the test script to verify the error handling system:

```
npx ts-node src/errors/error-system.test.ts
```