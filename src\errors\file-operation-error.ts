/**
 * Error class for file operations
 */

import { AppError } from './app-error';

export class FileOperationError extends AppError {
  readonly code = 'FILE_OPERATION_ERROR';
  readonly userMessage: string;
  readonly exitCode = 4;
  readonly filePath: string;
  
  constructor(
    message: string,
    userMessage: string,
    filePath: string,
    details?: Record<string, any>
  ) {
    super(message, details);
    this.userMessage = userMessage;
    this.filePath = filePath;
  }
  
  static notFound(filePath: string, details?: Record<string, any>): FileOperationError {
    return new FileOperationError(
      `File not found: ${filePath}`,
      `The file "${filePath}" was not found.`,
      filePath,
      details
    );
  }
  
  static permissionDenied(filePath: string, details?: Record<string, any>): FileOperationError {
    return new FileOperationError(
      `Permission denied: ${filePath}`,
      `Permission denied for file "${filePath}". Please check file permissions.`,
      filePath,
      details
    );
  }
  
  static readError(filePath: string, error?: Error, details?: Record<string, any>): FileOperationError {
    return new FileOperationError(
      `Failed to read file: ${filePath}`,
      `Failed to read file "${filePath}". Please check if the file exists and is accessible.`,
      filePath,
      {
        originalError: error?.message,
        ...details
      }
    );
  }
  
  static writeError(filePath: string, error?: Error, details?: Record<string, any>): FileOperationError {
    return new FileOperationError(
      `Failed to write file: ${filePath}`,
      `Failed to write to file "${filePath}". Please check disk space and permissions.`,
      filePath,
      {
        originalError: error?.message,
        ...details
      }
    );
  }
  
  static directoryCreationError(dirPath: string, error?: Error, details?: Record<string, any>): FileOperationError {
    return new FileOperationError(
      `Failed to create directory: ${dirPath}`,
      `Failed to create directory "${dirPath}". Please check permissions.`,
      dirPath,
      {
        originalError: error?.message,
        ...details
      }
    );
  }
}