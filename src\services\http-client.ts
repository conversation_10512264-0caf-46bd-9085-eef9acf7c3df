/**
 * Centralized HTTP client configuration with unified User-Agent
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { defaultConfig } from '../config/app-config';
import { logger } from '../utils/logger';

/**
 * Create a configured Axios instance with unified User-Agent and default settings
 */
export function createHttpClient(baseURL?: string, additionalConfig?: AxiosRequestConfig): AxiosInstance {
  const config: AxiosRequestConfig = {
    timeout: defaultConfig.http.timeout,
    headers: {
      'User-Agent': defaultConfig.http.userAgent,
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    ...additionalConfig
  };

  if (baseURL) {
    config.baseURL = baseURL;
  }

  const client = axios.create(config);

  // Add request interceptor for logging
  client.interceptors.request.use(
    (config) => {
      logger.debug(`HTTP Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        userAgent: config.headers?.['User-Agent']
      });
      return config;
    },
    (error) => {
      logger.error('HTTP Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor for logging
  client.interceptors.response.use(
    (response) => {
      logger.debug(`HTTP Response: ${response.status} ${response.config.url}`, {
        status: response.status,
        statusText: response.statusText
      });
      return response;
    },
    (error) => {
      logger.error('HTTP Response Error:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        message: error.message
      });
      return Promise.reject(error);
    }
  );

  return client;
}

/**
 * Default HTTP client instance
 */
export const httpClient = createHttpClient();

/**
 * Create HTTP client specifically for Confluence API calls
 */
export function createConfluenceHttpClient(baseURL: string, additionalHeaders?: Record<string, string>): AxiosInstance {
  return createHttpClient(baseURL, {
    headers: {
      'User-Agent': defaultConfig.http.userAgent,
      'X-Atlassian-Token': 'no-check',
      ...additionalHeaders
    }
  });
}
