/**
 * Lazy-loaded Puppeteer manager with proper resource cleanup
 */

import { PerformanceUtils, LazyModule, ResourceCleanup } from '../utils/performance-utils';
import { logger } from '../utils/logger';
import { withErrorHandling } from '../utils/error-handler';
import { NetworkError } from '../errors';
import { defaultConfig } from '../config/app-config';

// Use any types to avoid complex Puppeteer type conflicts
type PuppeteerBrowser = any;
type PuppeteerPage = any;
type PuppeteerModule = any;

export class LazyPuppeteerManager implements ResourceCleanup {
  private puppeteerModule: LazyModule<PuppeteerModule>;
  private activeBrowsers = new Set<PuppeteerBrowser>();
  private activePages = new Set<PuppeteerPage>();
  private cleanupRegistered = false;

  constructor() {
    // Create lazy-loaded Puppeteer module
    this.puppeteerModule = PerformanceUtils.createLazyModule(
      'puppeteer',
      async () => {
        logger.info('Loading Puppeteer module...');
        PerformanceUtils.logMemoryUsage('before-puppeteer-load');

        const puppeteer = await import('puppeteer');

        PerformanceUtils.logMemoryUsage('after-puppeteer-load');
        return puppeteer.default || puppeteer;
      },
      async (module) => {
        // Cleanup all browsers when unloading
        await this.cleanup();
      }
    );

    // Register cleanup on first use
    if (!this.cleanupRegistered) {
      PerformanceUtils.registerCleanup(this);
      this.cleanupRegistered = true;
    }
  }

  /**
   * Launch a new browser instance with optimized settings
   */
  async launchBrowser(options: any = {}): Promise<PuppeteerBrowser> {
    return withErrorHandling(async () => {
      const puppeteer = await this.puppeteerModule.load();

      // Optimized browser launch options
      const defaultOptions = {
        headless: false,
        defaultViewport: null,
        args: [
          '--start-maximized',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage', // Overcome limited resource problems
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu', // Applicable to Windows
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
        `--user-agent=${defaultConfig.http.userAgent}`
        ],
        ...options
      };

      logger.debug('Launching browser with options:', defaultOptions);
      PerformanceUtils.logMemoryUsage('before-browser-launch');

      const browser = await PerformanceUtils.withTimeout(
        () => puppeteer.launch(defaultOptions),
        30000, // 30 second timeout
        'Browser launch timed out'
      );

      // Track the browser for cleanup
      this.activeBrowsers.add(browser);

      PerformanceUtils.logMemoryUsage('after-browser-launch');
      logger.debug(`Browser launched successfully. Active browsers: ${this.activeBrowsers.size}`);

      // Set up browser cleanup on disconnect
      if ((browser as any).on) {
        (browser as any).on('disconnected', () => {
          this.activeBrowsers.delete(browser);
          logger.debug(`Browser disconnected. Active browsers: ${this.activeBrowsers.size}`);
        });
      }

      return browser;
    }, {
      context: 'browser launch',
      exitOnError: false,
      logger
    });
  }

  /**
   * Create a new page with optimized settings
   */
  async createPage(browser: PuppeteerBrowser): Promise<PuppeteerPage> {
    return withErrorHandling(async () => {
      const page = await browser.newPage();

      // Track the page for cleanup
      this.activePages.add(page);

      // Set up page optimizations
      if (page.setDefaultTimeout) {
        await page.setDefaultTimeout(30000);
      }
      if (page.setDefaultNavigationTimeout) {
        await page.setDefaultNavigationTimeout(30000);
      }

      // Disable images and CSS for faster loading (optional)
      if (process.env.PUPPETEER_FAST_MODE === 'true' && page.setRequestInterception) {
        await page.setRequestInterception(true);
        page.on?.('request', (req: any) => {
          const resourceType = req.resourceType();
          if (resourceType === 'stylesheet' || resourceType === 'image') {
            req.abort();
          } else {
            req.continue();
          }
        });
      }

      logger.debug(`Page created. Active pages: ${this.activePages.size}`);

      // Set up page cleanup on close
      page.on?.('close', () => {
        this.activePages.delete(page);
        logger.debug(`Page closed. Active pages: ${this.activePages.size}`);
      });

      return page;
    }, {
      context: 'page creation',
      exitOnError: false,
      logger
    });
  }

  /**
   * Navigate to URL with retry logic and error handling
   */
  async navigateToUrl(
    page: PuppeteerPage,
    url: string,
    options: any = {}
  ): Promise<any> {
    return withErrorHandling(async () => {
      const defaultOptions = {
        waitUntil: 'networkidle2',
        timeout: 30000,
        ...options
      };

      logger.debug(`Navigating to: ${url}`);

      const response = await PerformanceUtils.withTimeout(
        () => page.goto(url, defaultOptions),
        defaultOptions.timeout + 5000,
        `Navigation to ${url} timed out`
      );

      if (!response || !(response as any).ok?.()) {
        throw new NetworkError(
          `Failed to navigate to ${url}`,
          `Navigation failed with status: ${(response as any)?.status?.() || 'unknown'}`
        );
      }

      logger.debug(`Successfully navigated to: ${url}`);
      return response;
    }, {
      context: 'page navigation',
      exitOnError: false,
      logger
    });
  }

  /**
   * Wait for selector with timeout and retry logic
   */
  async waitForSelector(
    page: PuppeteerPage,
    selector: string,
    options: any = {}
  ): Promise<any> {
    return withErrorHandling(async () => {
      const defaultOptions = {
        timeout: 10000,
        visible: true,
        ...options
      };

      logger.debug(`Waiting for selector: ${selector}`);

      const element = await PerformanceUtils.withTimeout(
        () => page.waitForSelector(selector, defaultOptions),
        defaultOptions.timeout + 2000,
        `Selector ${selector} not found within timeout`
      );

      logger.debug(`Selector found: ${selector}`);
      return element;
    }, {
      context: 'selector waiting',
      exitOnError: false,
      logger
    });
  }

  /**
   * Validate session using API endpoint
   */
  async validateSessionViaAPI(page: PuppeteerPage, baseUrl: string): Promise<boolean> {
    return withErrorHandling(async () => {
      logger.debug('Validating session via API...');

      const response = await page.evaluate(async (url: string) => {
        try {
          const timestamp = new Date().getTime();
          const res = await fetch(`${url}/rest/api/user/current?_=${timestamp}`, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache'
            }
          });
          return { status: res.status, ok: res.ok };
        } catch (error: any) {
          return { status: 0, ok: false, error: error?.message || 'Unknown error' };
        }
      }, baseUrl);

      const isValid = response.ok && response.status === 200;
      logger.debug(`API validation result: ${isValid ? 'valid' : 'invalid'} (status: ${response.status})`);

      return isValid;
    }, {
      context: 'session validation',
      exitOnError: false,
      logger
    });
  }

  /**
   * Get all cookies from the current page
   */
  async getCookies(page: PuppeteerPage): Promise<any[]> {
    return withErrorHandling(async () => {
      const client = await page.target().createCDPSession();
      const { cookies } = await client.send('Network.getAllCookies');

      logger.debug(`Retrieved ${cookies.length} cookies from browser`);
      return cookies;
    }, {
      context: 'cookie retrieval',
      exitOnError: false,
      logger
    });
  }

  /**
   * Close a specific page
   */
  async closePage(page: PuppeteerPage): Promise<void> {
    return withErrorHandling(async () => {
      if (this.activePages.has(page)) {
        await page.close();
        this.activePages.delete(page);
        logger.debug(`Page closed. Active pages: ${this.activePages.size}`);
      }
    }, {
      context: 'page closing',
      exitOnError: false,
      logger
    });
  }

  /**
   * Close a specific browser
   */
  async closeBrowser(browser: PuppeteerBrowser): Promise<void> {
    return withErrorHandling(async () => {
      if (this.activeBrowsers.has(browser)) {
        // Close all pages first
        const pages = await browser.pages();
        await Promise.all(pages.map((page: any) => {
          if (this.activePages.has(page)) {
            this.activePages.delete(page);
          }
          return page.close().catch(() => {}); // Ignore errors
        }));

        await browser.close();
        this.activeBrowsers.delete(browser);
        logger.debug(`Browser closed. Active browsers: ${this.activeBrowsers.size}`);

        PerformanceUtils.logMemoryUsage('after-browser-close');
      }
    }, {
      context: 'browser closing',
      exitOnError: false,
      logger
    });
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    logger.info('Cleaning up Puppeteer resources...');

    // Close all pages
    const pageCleanupPromises = Array.from(this.activePages).map(async (page) => {
      try {
        await page.close();
      } catch (error) {
        logger.debug('Error closing page during cleanup:', error);
      }
    });

    await Promise.allSettled(pageCleanupPromises);
    this.activePages.clear();

    // Close all browsers
    const browserCleanupPromises = Array.from(this.activeBrowsers).map(async (browser) => {
      try {
        if (browser.isConnected()) {
          await browser.close();
        }
      } catch (error) {
        logger.debug('Error closing browser during cleanup:', error);
      }
    });

    await Promise.allSettled(browserCleanupPromises);
    this.activeBrowsers.clear();

    // Unload the Puppeteer module
    this.puppeteerModule.unload();

    // Force garbage collection
    PerformanceUtils.forceGarbageCollection();

    logger.info('Puppeteer cleanup completed');
    PerformanceUtils.logMemoryUsage('after-puppeteer-cleanup');
  }

  /**
   * Get resource statistics
   */
  getStats(): { activeBrowsers: number; activePages: number; moduleLoaded: boolean } {
    return {
      activeBrowsers: this.activeBrowsers.size,
      activePages: this.activePages.size,
      moduleLoaded: this.puppeteerModule.isLoaded()
    };
  }
}

// Export singleton instance
export const lazyPuppeteerManager = new LazyPuppeteerManager();