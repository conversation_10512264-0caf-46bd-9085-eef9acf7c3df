# Confluence Page Update CLI Tool

## Overview

This CLI tool allows you to securely manage Confluence credentials and update page content by converting Markdown files to Confluence-compatible HTML. It supports two authentication methods:

- API Token-based authentication
- Browser-based authentication with Puppeteer (for instances requiring interactive login)

Key features:

- Secure credential storage with encryption
- Simplified session management with centralized cookie storage
- Automatic 401-retry logic for seamless re-authentication
- Custom Markdown rendering for code blocks
- Multi-instance support

For detailed use cases, see [USE-CASES.md](docs/USE-CASES.md).

## Installation

1. **Prerequisites**:

   - Node.js >= 18
   - npm or yarn
2. **SSH Configuration** (Optional):
   If you are using PuTTYs Pageant with GitHub, add your private key to the Pagent and set the following

   ```bash
   $env:GIT_SSH="C:\Program Files\PuTTY\plink.exe"  # Windows
   git config --global ssh.variant plink
   ```
3. **<PERSON>lone and Install**:
   Clone and install the tool:

   ```bash
   <NAME_EMAIL>:danielfrey63/confluence-page-update.git
   cd confluence-page-update
   npm install
   npm run build  # Compiles TypeScript to JavaScript
   ```
4. **Global Installation (Optional)**:
   If you want to make `confluence-upload` and `confluence-credentials` available globally, run the following command:

   ```bash
   npm link
   ```

## Credential Management

Use the `confluence-credentials` CLI to manage Confluence instance credentials. This covers Use Cases UC-001 to UC-005. **Password Required**: The credential manager now requires a master password for enhanced security.

### Run the Credential Manager

```bash
node dist/credential-manager-cli.js
# Or if globally installed: confluence-credentials
# Or: npm run credentials
```

**Password Authentication**: You'll be prompted for your master password:

```
Confluence Credential Manager
=============================
Enter master password: ********
```

This launches an interactive menu:

```
Options:
1. List all credentials
2. Add/Update credential
3. Remove credential
4. Import from JSON file
5. Export credentials to JSON
6. Exit
```

### UC-001: List All Credentials

- **Command**: Select option 1 from the menu.
- **Example Output**:
  ```
  Stored Credentials:
  1. TechWave (https://intranet.techwave.com)
     Space Key: TWD
     Auth Method: API Token

  2. GlobalSoft (https://confluence.globalsoft.com)
     Space Key: GSDEV
     Auth Method: Browser Login
     Username: <EMAIL>
  ```
- **Notes**: Displays all stored credentials. If none exist, shows "No credentials stored."

### UC-002: Add or Update Credential

- **Command**: Select option 2 from the menu.
- **Interactive Prompts**:
  ```
  Add/Update Credential
  ====================

  Name (e.g., "Company Confluence"): TechWave
  Base URL (e.g., "https://confluence.example.com"): https://intranet.techwave.com
  Space Key: TWD
  Authentication Method (1 for API Token, 2 for Browser Login): 1
  API Token: tw_api_8f7d3e2a1c9b5
  ```
- **Notes**: If the baseUrl exists, it updates; otherwise, adds new. For Browser Login (option 2), prompts for username and optional password.

### UC-003: Remove Credential

- **Command**: Select option 3 from the menu.
- **Interactive Prompts**:
  ```
  Select credential to remove:
  1. TechWave (https://intranet.techwave.com)
  2. GlobalSoft (https://confluence.globalsoft.com)

  Enter number (1-2): 1
  Are you sure you want to remove "TechWave"? (y/n): y
  Credential for TechWave removed successfully.
  ```
- **Notes**: Requires confirmation to prevent accidental deletion.

### UC-004: Import Credentials from JSON

- **Command**: Select option 4 from the menu, or use `--import <file>` command-line argument.
- **Interactive Prompts**:
  ```
  Enter path to JSON file [./config.json]: credentials-backup.json
  Found 3 valid credential(s) in the file.
  Proceed with import? This will replace all existing credentials. (y/n): y
  Import successful!
  ```
- **Command-line Usage**:
  ```bash
  node dist/credential-manager-cli.js --import ./credentials-backup.json
  # Or: npm run credentials -- --import ./credentials-backup.json
  ```
- **Notes**: Supports any JSON file path. Validates required fields and file format before importing. Overwrites existing credentials after confirmation.

### UC-005: Export Credentials to JSON

- **Command**: Select option 5 from the menu.
- **Interactive Prompts**:
  ```
  Export Credentials
  ==================
  Found 3 credential(s) to export.
  Enter export file path [./credentials-export.json]: credentials-backup.json
  Warning: Exported file will contain sensitive data in plain text. Continue? (y/n): y
  Export completed successfully!
  Remember to store the exported file securely and delete it when no longer needed.
  ```
- **Notes**: Exports all stored credentials to a plain JSON file for backup or migration. Contains sensitive data in plain text, so handle securely.

## Updating Confluence Pages

Use the main script `update_confluence_page.js` or the optimized version `update-confluence-page-cli.js` for Use Case UC-008, which incorporates authentication (UC-006 to UC-007).

### Run the Update Script

```bash
# Standard version
npm run start [pageId] [markdownFile] [instanceName]
# Or if globally installed: confluence-upload [pageId] [markdownFile] [instanceName]
# Or directly: node dist/update_confluence_page.js [pageId] [markdownFile] [instanceName]

# Optimized version with better performance
npm run start:optimized [pageId] [markdownFile] [instanceName]
# Or if globally installed: confluence-upload-optimized [pageId] [markdownFile] [instanceName]
# Or directly: node dist/cli/update-confluence-page-cli.js [pageId] [markdownFile] [instanceName]
```

- **Parameters**:
  - `pageId`: Confluence page ID (required)
  - `markdownFile`: Path to Markdown file (required)
  - `instanceName`: Confluence instance name (e.g., "TechWave", "GlobalSoft") or base URL (optional; prompts if missing)

### UC-008: Update Confluence Page Content (with Authentication)

- **Example with Parameters**:
  ```bash
  # Using configuration name (quotes optional but recommended for names with spaces)
  npm run start 12345678 path/to/my-markdown.md "GlobalSoft"
  npm run start 87654321 path/to/my-markdown.md "TechWave"
  npm run start 87654321 path/to/my-markdown.md TechWave

  # Or with full URL (backward compatibility)
  npm run start 12345678 path/to/my-markdown.md https://confluence.globalsoft.com

  # Or directly: node dist/update_confluence_page.js 12345678 path/to/my-markdown.md "GlobalSoft"
  ```
- **Interactive Mode (if params missing)**:
  ```
  Verfügbare Confluence Instanzen:
  1: TechWave
  2: GlobalSoft

  Bitte Confluence Instanz wählen [1-2]: 2
  Bitte die Confluence Page ID eingeben: 12345678
  Bitte den Pfad zur Markdown-Datei angeben: path/to/my-markdown.md
  ```
- **Authentication Flow** (UC-006 to UC-007):
  - If API Token configured: Uses Bearer token automatically (UC-006).
  - If Browser Login configured:
    - Loads existing encrypted cookies without validation (UC-007).
    - If no cookies exist: Launches browser for manual login (UC-006).
    - If 401 error occurs: Automatically triggers new login and retries.
    - Saves/updates cookies in encrypted centralized location for future use.
- **Environment Variable**:
  - `FORCE_CONFLUENCE_LOGIN=true`: Forces manual browser login even if cookies exist.
- **Output**:
  ```
  Fetching current page content for ID: 12345678
  Current page version: 5
  Updating page ID: 12345678 to version 6
  ✅ Confluence page updated successfully!
  Link: https://confluence.globalsoft.com/pages/viewpage.action?pageId=12345678
  ```
- **Notes**: Converts Markdown to Confluence storage format with custom code block handling. Increments page version automatically.

## Advanced Usage

### Troubleshooting

- **Invalid Cookies**: Automatically triggers re-login on 401 errors with retry logic.
- **Authentication Errors**: Check console logs; ensure credentials are set via manager with correct password.
- **API Issues**: Verify page ID and permissions in Confluence.
- **Cookie Location**: All cookies are stored encrypted in `$USERHOME/.confluence-uploader/cookies.enc`.
- **Password Issues**: If you forget your master password, you'll need to delete the `.confluence-uploader` directory and reconfigure.

### Error Handling

The application now uses a standardized error handling system with typed errors:

- **AuthenticationError**: For password, token, or login failures
- **FileOperationError**: For file access, permission, or I/O issues
- **NetworkError**: For API connectivity or HTTP status issues
- **ValidationError**: For invalid input or configuration
- **CryptoError**: For encryption/decryption failures
- **ApplicationError**: For general application errors

Each error provides:

- Clear user-facing messages
- Consistent exit codes
- Detailed error information for troubleshooting
- Suggested resolution steps when applicable

### Security Notes

- **Credential Storage**: All sensitive data (API tokens, usernames, passwords) is encrypted using AES-256-GCM and stored in `~/.confluence-uploader/credentials.enc`.
- **Master Key Protection**: The master key is now encrypted with your password using PBKDF2 (100,000 iterations) and stored in `~/.confluence-uploader/.key`. Password is required for all operations.
- **Cookie Encryption**: Session cookies are encrypted using the same AES-256-GCM method and stored in `~/.confluence-uploader/cookies.enc`.
- **Migration**: Legacy plain files are automatically migrated to encrypted format on first use with password.
- **Best Practices**:
  - Use a strong, unique master password.
  - Use full-disk encryption on your machine.
  - Regularly rotate API tokens via the credential manager.
  - Avoid running on shared or untrusted machines.
  - Keep your master password secure - if lost, you'll need to reconfigure everything.
- **Backup**: Use the export function to create secure backups of your credentials, then store the exported file safely.
- **Imports**: Plain JSON imports are encrypted immediately – delete originals after import.

## Performance Optimizations

This tool includes several performance optimizations for better resource management and faster execution:

### Key Features

- **Lazy Loading**: Heavy dependencies (like Puppeteer) are only loaded when needed
- **Memory Management**: Automatic cleanup of sensitive data and browser resources
- **Resource Monitoring**: Built-in memory usage tracking and logging
- **Optimized File Operations**: Streaming support for large files
- **Secure Memory Handling**: Automatic wiping of sensitive data from memory

### Optimized CLI

Use the optimized version for better performance:

```bash
# Optimized version with lazy loading and better resource management
confluence-upload-optimized [pageId] [markdownFile] [instanceName]

# Or directly
node dist/cli/update-confluence-page-cli.js [pageId] [markdownFile] [instanceName]
```

### Performance Benefits

- **Faster Startup**: ~50-70% faster startup time when browser automation isn't needed
- **Lower Memory Usage**: ~50MB less memory usage when Puppeteer isn't required
- **Better Resource Cleanup**: Automatic cleanup prevents memory leaks
- **Monitoring**: Built-in performance monitoring and logging

For detailed information about performance optimizations, see [PERFORMANCE-OPTIMIZATIONS.md](docs/PERFORMANCE-OPTIMIZATIONS.md).

## Project Structure

The project has been setup with a clean, modular architecture:

```
src/
├── cli/                     # Command-line interface modules
│   ├── index.ts
│   └── update-confluence-page-cli.ts
├── config/                  # Configuration and constants
│   ├── app-config.ts        # Centralized configuration
│   └── messages.ts          # User-facing messages
├── errors/                  # Typed error system
│   ├── app-error.ts         # Base error class
│   ├── authentication-error.ts
│   ├── crypto-error.ts
│   ├── file-operation-error.ts
│   ├── network-error.ts
│   └── validation-error.ts
├── managers/                # Business logic managers
│   ├── confluence-manager.ts # Confluence operations manager
│   └── index.ts
├── services/                # Service layer
│   ├── index.ts
│   ├── lazy-puppeteer-manager.ts
│   ├── secure-cookie-manager.ts
│   └── secure-credentials-manager.ts
├── types/                   # TypeScript type definitions
│   └── index.ts
├── utils/                   # Utility functions
│   ├── cli-utils.ts         # CLI helper functions
│   ├── crypto-utils.ts      # Encryption utilities
│   ├── error-handler.ts     # Error handling utilities
│   ├── file-utils.ts        # File operation utilities
│   ├── index.ts
│   ├── logger.ts            # Centralized logging
│   ├── password-input.ts    # Password input handling
│   ├── performance-test.ts  # Performance testing utilities
│   ├── performance-utils.ts # Performance optimization utilities
│   └── validation.ts        # Input validation utilities
└── index.ts                 # Main entry point
```

## Documentation

This project includes comprehensive documentation covering various aspects of the system:

### Core Documentation

- **[USE-CASES.md](docs/USE-CASES.md)** - Detailed use cases and system overview
- **[PERFORMANCE-OPTIMIZATIONS.md](docs/PERFORMANCE-OPTIMIZATIONS.md)** - Performance optimization guide and benchmarks
- **[REFACTORING-SUMMARY.md](docs/REFACTORING-SUMMARY.md)** - Complete refactoring summary and validation results

### Development Documentation

- **[TODOS.md](docs/TODOS.md)** - Project tasks, completed items, and future improvements
- **[HTTP-USER-AGENT.md](docs/HTTP-USER-AGENT.md)** - HTTP User-Agent standardization implementation

### Contributing

See the use cases document for system details. Report issues or contribute via pull requests.
