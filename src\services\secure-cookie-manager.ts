/**
 * Secure cookie manager for storing and retrieving browser cookies
 */

import { CryptoUtils, EncryptedMasterKey, EncryptedData } from '../utils/crypto-utils';
import { FileUtils } from '../utils/file-utils';
import { logger } from '../utils/logger';
import { defaultConfig } from '../config/app-config';
import { messages } from '../config/messages';
import { withErrorHandling } from '../utils/error-handler';
import { CryptoError } from '../errors';
import { PerformanceUtils, ResourceCleanup } from '../utils/performance-utils';

export interface EnhancedCookie {
    name: string;
    value: string;
    domain: string;
    path?: string;
    expires?: number;
    httpOnly?: boolean;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
    savedAt: number;
    baseUrl: string;
}

export interface CookieStorage {
    cookies: EnhancedCookie[];
    version: string;
}

export interface EncryptedCookieStorage extends EncryptedData {
    version: string;
    encrypted: true;
}

class SecureCookieManager implements ResourceCleanup {
    private readonly keyFilePath: string;
    private readonly cookieFilePath: string;
    private encryptionKey?: Buffer;
    private cookies: EnhancedCookie[] = [];
    private cleanupRegistered = false;
    
    constructor() {
        // Use centralized configuration for file paths
        this.keyFilePath = defaultConfig.paths.keyFile;
        this.cookieFilePath = defaultConfig.paths.cookiesFile;
        
        // Register cleanup on first use
        if (!this.cleanupRegistered) {
            PerformanceUtils.registerCleanup(this);
            this.cleanupRegistered = true;
        }
    }
    
    /**
     * Check if the key file exists and is encrypted (new format)
     */
    private async isKeyFileEncrypted(): Promise<boolean> {
        return withErrorHandling(async () => {
            try {
                const keyContent = await FileUtils.readFile(this.keyFilePath);
                const parsed = JSON.parse(keyContent);
                return CryptoUtils.isEncryptedMasterKey(parsed);
            } catch {
                return false;
            }
        }, {
            context: 'checking key file encryption',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Check if cookie file is encrypted
     */
    private async isCookieFileEncrypted(): Promise<boolean> {
        return withErrorHandling(async () => {
            try {
                const cookieContent = await FileUtils.readFile(this.cookieFilePath);
                const parsed = JSON.parse(cookieContent);
                return parsed.encrypted === true && 
                       typeof parsed.salt === 'string' && 
                       typeof parsed.iv === 'string' && 
                       typeof parsed.encryptedData === 'string';
            } catch {
                return false;
            }
        }, {
            context: 'checking cookie file encryption',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Initialize the cookie manager with the same master key as credentials
     */
    async initialize(password?: string): Promise<void> {
        return withErrorHandling(async () => {
            const secureConfigDir = defaultConfig.paths.configDir;
            
            // Create secure config directory if it doesn't exist
            await FileUtils.ensureDirectory(secureConfigDir);
            
            // Check if key file exists
            const keyFileExists = await FileUtils.fileExists(this.keyFilePath);
            
            if (keyFileExists) {
                // Key file exists - check if it's encrypted or plain
                const isEncrypted = await this.isKeyFileEncrypted();
                
                if (isEncrypted) {
                    // New encrypted format - password required
                    if (!password) {
                        throw new CryptoError(
                            'Password required to decrypt master key for cookie encryption',
                            messages.errors.passwordRequired
                        );
                    }
                    
                    const keyContent = await FileUtils.readFile(this.keyFilePath);
                    const encryptedMasterKey: EncryptedMasterKey = JSON.parse(keyContent);
                    
                    try {
                        this.encryptionKey = CryptoUtils.decryptMasterKey(encryptedMasterKey, password);
                    } catch (error) {
                        throw new CryptoError(
                            'Invalid password or corrupted key file',
                            messages.errors.invalidPassword,
                            { originalError: error instanceof Error ? error.message : String(error) }
                        );
                    }
                } else {
                    // Old plain format - read directly
                    this.encryptionKey = await FileUtils.readBinaryFile(this.keyFilePath);
                }
            } else {
                throw new CryptoError(
                    'Master key not found',
                    messages.errors.keyFileNotFound
                );
            }
            
            // Try to load existing cookies
            try {
                await this.loadCookies();
            } catch (err) {
                // No cookies file or invalid format - start with empty cookies
                this.cookies = [];
            }
            
            logger.debug('Cookie manager initialized successfully');
        }, {
            context: 'cookie manager initialization',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Save cookies to encrypted file using the same encryption method as credentials
     */
    private async saveCookiesEncrypted(): Promise<void> {
        return withErrorHandling(async () => {
            if (!this.encryptionKey) {
                throw new CryptoError(
                    'Encryption key not initialized',
                    messages.errors.encryptionKeyNotInitialized
                );
            }
            
            const cookieStorage: CookieStorage = {
                cookies: this.cookies,
                version: '2.0'
            };
            
            const cookiesJson = JSON.stringify(cookieStorage);
            const encryptedData = CryptoUtils.encryptData(cookiesJson, this.encryptionKey);
            
            // Add cookie-specific fields to the encrypted data
            const encryptedStorage: EncryptedCookieStorage = {
                ...encryptedData,
                version: '2.0',
                encrypted: true
            };
            
            await FileUtils.writeJsonFile(this.cookieFilePath, encryptedStorage, { mode: 0o600 });
        }, {
            context: 'cookie saving',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Load cookies from encrypted file
     */
    private async loadCookiesEncrypted(): Promise<void> {
        return withErrorHandling(async () => {
            if (!this.encryptionKey) {
                throw new CryptoError(
                    'Encryption key not initialized',
                    messages.errors.encryptionKeyNotInitialized
                );
            }
            
            const encryptedContent = await FileUtils.readFile(this.cookieFilePath);
            const encryptedStorage: EncryptedCookieStorage = JSON.parse(encryptedContent);
            
            // Extract the standard encrypted data fields
            const encryptedData: EncryptedData = {
                salt: encryptedStorage.salt,
                iv: encryptedStorage.iv,
                encryptedData: encryptedStorage.encryptedData
            };
            
            const decrypted = CryptoUtils.decryptData(encryptedData, this.encryptionKey);
            const cookieStorage: CookieStorage = JSON.parse(decrypted);
            this.cookies = cookieStorage.cookies || [];
        }, {
            context: 'cookie loading',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Load cookies from encrypted file
     */
    private async loadCookies(): Promise<void> {
        return withErrorHandling(async () => {
            try {
                const cookieFileExists = await FileUtils.fileExists(this.cookieFilePath);
                if (!cookieFileExists) {
                    this.cookies = [];
                    return;
                }
                
                const isEncrypted = await this.isCookieFileEncrypted();
                if (isEncrypted) {
                    await this.loadCookiesEncrypted();
                    return;
                }
                
                // No valid cookie format found
                this.cookies = [];
            } catch (error) {
                logger.debug('Error loading cookies, starting with empty cookie store', error);
                this.cookies = [];
            }
        }, {
            context: 'cookie loading',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Get all cookies
     */
    getCookies(): EnhancedCookie[] {
        return [...this.cookies];
    }
    
    /**
     * Get cookies for a specific base URL
     */
    getCookiesForBaseUrl(baseUrl: string): EnhancedCookie[] {
        try {
            const baseDomain = new URL(baseUrl).hostname;
            return this.cookies.filter(cookie => {
                // The cookie must belong to the same baseUrl context.
                if (cookie.baseUrl !== baseUrl) {
                    return false;
                }

                // The request domain must match the cookie's domain or be a subdomain of it.
                // A cookie for ".example.com" should match "sub.example.com".
                const cookieDomain = cookie.domain.startsWith('.') ? cookie.domain.substring(1) : cookie.domain;
                return baseDomain.endsWith(cookieDomain);
            });
        } catch (error) {
            logger.error('Error filtering cookies:', error);
            return [];
        }
    }
    
    /**
     * Save cookies with encryption
     */
    async saveCookies(cookies: any[], baseUrl: string): Promise<void> {
        return withErrorHandling(async () => {
            const baseDomain = new URL(baseUrl).hostname;
            const now = Date.now();
            
            const enhancedCookies: EnhancedCookie[] = cookies
                .filter(c => c.domain.includes(baseDomain))
                .map(cookie => ({
                    name: cookie.name,
                    value: cookie.value,
                    domain: cookie.domain,
                    path: cookie.path,
                    expires: cookie.expires,
                    httpOnly: cookie.httpOnly,
                    secure: cookie.secure,
                    sameSite: cookie.sameSite,
                    savedAt: now,
                    baseUrl: baseUrl
                }));
            
            // Replace cookies for this baseUrl
            this.cookies = this.cookies.filter(c => c.baseUrl !== baseUrl);
            this.cookies.push(...enhancedCookies);
            
            await this.saveCookiesEncrypted();
            logger.info(messages.info.cookiesSaved(enhancedCookies.length, baseDomain));
        }, {
            context: 'cookie saving',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Clear all cookies
     */
    async clearCookies(): Promise<void> {
        return withErrorHandling(async () => {
            this.cookies = [];
            await this.saveCookiesEncrypted();
            logger.info(messages.info.cookiesCleared);
        }, {
            context: 'cookie clearing',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Clear cookies for a specific base URL
     */
    async clearCookiesForBaseUrl(baseUrl: string): Promise<void> {
        return withErrorHandling(async () => {
            const initialLength = this.cookies.length;
            this.cookies = this.cookies.filter(c => c.baseUrl !== baseUrl);
            
            if (this.cookies.length !== initialLength) {
                await this.saveCookiesEncrypted();
                logger.info(`Cookies cleared for ${baseUrl}`);
            }
        }, {
            context: 'cookie clearing for URL',
            exitOnError: false,
            logger
        });
    }
    
    /**
     * Check if cookies exist for a base URL
     */
    hasCookiesForBaseUrl(baseUrl: string): boolean {
        return this.getCookiesForBaseUrl(baseUrl).length > 0;
    }
    
    /**
     * Get cookie statistics
     */
    getCookieStats(): { total: number; byDomain: Record<string, number>; encrypted: boolean } {
        try {
            const byDomain: Record<string, number> = {};
            
            this.cookies.forEach(cookie => {
                byDomain[cookie.domain] = (byDomain[cookie.domain] || 0) + 1;
            });
            
            return {
                total: this.cookies.length,
                byDomain,
                encrypted: true
            };
        } catch (error) {
            logger.error('Error getting cookie statistics:', error);
            return {
                total: 0,
                byDomain: {},
                encrypted: true
            };
        }
    }

    /**
     * Cleanup sensitive data from memory
     */
    async cleanup(): Promise<void> {
        logger.debug('Cleaning up SecureCookieManager...');
        
        // Clear cookies array
        this.cookies.length = 0;
        
        // Securely wipe encryption key
        if (this.encryptionKey) {
            CryptoUtils.secureWipe(this.encryptionKey);
            this.encryptionKey = undefined;
        }
        
        // Force garbage collection
        PerformanceUtils.forceGarbageCollection();
        
        logger.debug('SecureCookieManager cleanup completed');
    }
}

export { SecureCookieManager };