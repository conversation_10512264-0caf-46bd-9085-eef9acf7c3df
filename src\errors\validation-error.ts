/**
 * Error class for validation failures
 */

import { AppError } from './app-error';

export class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly userMessage: string;
  readonly exitCode = 2;
  
  constructor(message: string, userMessage: string, details?: Record<string, any>) {
    super(message, details);
    this.userMessage = userMessage;
  }
  
  static invalidFormat(field: string, details?: Record<string, any>): ValidationError {
    return new ValidationError(
      `Invalid format for ${field}`,
      `The format for ${field} is invalid.`,
      details
    );
  }
  
  static missingRequired(field: string, details?: Record<string, any>): ValidationError {
    return new ValidationError(
      `Missing required field: ${field}`,
      `The required field '${field}' is missing.`,
      details
    );
  }
  
  static invalidValue(field: string, value: any, details?: Record<string, any>): ValidationError {
    return new ValidationError(
      `Invalid value for ${field}: ${value}`,
      `The value for ${field} is invalid.`,
      { value, ...details }
    );
  }
}