/**
 * Tests for credential-manager-core.ts (testable CLI logic)
 */

import { CredentialManagerCLI, CLIOptions } from '../../src/cli/credential-manager-core';
import * as fs from 'fs/promises';

// Mock dependencies
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger');
jest.mock('fs/promises');

const mockFs = fs as jest.Mocked<typeof fs>;

describe('CredentialManagerCLI Core Logic', () => {
  let cli: CredentialManagerCLI;

  beforeEach(() => {
    jest.clearAllMocks();
    cli = new CredentialManagerCLI();
  });

  describe('parseArguments', () => {
    it('should parse import flag correctly', () => {
      const args = ['--import', 'test.json'];
      const options = cli.parseArguments(args);
      
      expect(options.importFilePath).toBe('test.json');
      expect(options.showHelp).toBeUndefined();
    });

    it('should parse short import flag correctly', () => {
      const args = ['-i', 'config.json'];
      const options = cli.parseArguments(args);
      
      expect(options.importFilePath).toBe('config.json');
    });

    it('should parse help flag correctly', () => {
      const args = ['--help'];
      const options = cli.parseArguments(args);
      
      expect(options.showHelp).toBe(true);
      expect(options.importFilePath).toBeUndefined();
    });

    it('should parse short help flag correctly', () => {
      const args = ['-h'];
      const options = cli.parseArguments(args);
      
      expect(options.showHelp).toBe(true);
    });

    it('should throw error for import flag without file path', () => {
      const args = ['--import'];
      
      expect(() => cli.parseArguments(args)).toThrow('--import flag requires a file path argument.');
    });

    it('should handle empty arguments', () => {
      const args: string[] = [];
      const options = cli.parseArguments(args);
      
      expect(options.importFilePath).toBeUndefined();
      expect(options.showHelp).toBeUndefined();
    });

    it('should handle multiple flags', () => {
      const args = ['--import', 'test.json', '--help'];
      const options = cli.parseArguments(args);
      
      expect(options.importFilePath).toBe('test.json');
      expect(options.showHelp).toBe(true);
    });
  });

  describe('getHelpMessage', () => {
    it('should return formatted help message', () => {
      const helpMessage = cli.getHelpMessage();
      
      expect(helpMessage).toContain('Usage: credential-manager-cli [options]');
      expect(helpMessage).toContain('--import, -i <file>');
      expect(helpMessage).toContain('--help, -h');
      expect(helpMessage).toContain('Examples:');
    });

    it('should include examples in help message', () => {
      const helpMessage = cli.getHelpMessage();
      
      expect(helpMessage).toContain('credential-manager-cli --import');
      expect(helpMessage).toContain('credential-manager-cli -i');
    });
  });

  describe('importCredentials', () => {
    beforeEach(() => {
      // Mock console methods
      jest.spyOn(console, 'log').mockImplementation();
      jest.spyOn(console, 'error').mockImplementation();
    });

    it('should import valid credentials from JSON array', async () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          token: 'test-token',
          puppeteerLogin: false
        }
      ];

      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await cli.importCredentials('test.json');

      expect(count).toBe(1);
      expect(mockFs.readFile).toHaveBeenCalledWith('test.json', 'utf8');
    });

    it('should import credentials from object with credentials array', async () => {
      const data = {
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            spaceKey: 'TEST'
          }
        ],
        version: '2.0'
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(data));

      const count = await cli.importCredentials('test.json');

      expect(count).toBe(1);
    });

    it('should handle empty credentials array', async () => {
      mockFs.readFile.mockResolvedValue(JSON.stringify([]));

      const count = await cli.importCredentials('test.json');

      expect(count).toBe(0);
    });

    it('should throw error for invalid JSON', async () => {
      mockFs.readFile.mockResolvedValue('invalid json');

      await expect(cli.importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should throw error for file not found', async () => {
      mockFs.readFile.mockRejectedValue(new Error('ENOENT: no such file or directory'));

      await expect(cli.importCredentials('nonexistent.json')).rejects.toThrow('File not found: nonexistent.json');
    });

    it('should validate credential structure', async () => {
      const invalidCredentials = [
        {
          name: 'Test Confluence'
          // Missing baseUrl
        }
      ];

      mockFs.readFile.mockResolvedValue(JSON.stringify(invalidCredentials));

      await expect(cli.importCredentials('test.json')).rejects.toThrow('Each credential must have at least name and baseUrl properties.');
    });

    it('should validate credentials array format', async () => {
      const invalidData = {
        credentials: 'not-an-array'
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(invalidData));

      await expect(cli.importCredentials('test.json')).rejects.toThrow('File must contain an array of credentials or an object with a credentials array.');
    });

    it('should handle credentials with missing optional fields', async () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net'
          // Missing optional fields like spaceKey, token
        }
      ];

      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await cli.importCredentials('test.json');

      expect(count).toBe(1);
    });
  });

  describe('Argument Validation', () => {
    it('should handle complex argument combinations', () => {
      const args = ['--import', 'file1.json', '--help', '--import', 'file2.json'];
      const options = cli.parseArguments(args);
      
      // Should take the last import file
      expect(options.importFilePath).toBe('file2.json');
      expect(options.showHelp).toBe(true);
    });

    it('should handle arguments with spaces in file paths', () => {
      const args = ['--import', 'my file with spaces.json'];
      const options = cli.parseArguments(args);
      
      expect(options.importFilePath).toBe('my file with spaces.json');
    });

    it('should handle relative and absolute paths', () => {
      const relativePath = './config/credentials.json';
      const absolutePath = '/home/<USER>/credentials.json';
      
      let options = cli.parseArguments(['--import', relativePath]);
      expect(options.importFilePath).toBe(relativePath);
      
      options = cli.parseArguments(['--import', absolutePath]);
      expect(options.importFilePath).toBe(absolutePath);
    });
  });

  describe('Error Handling', () => {
    it('should handle file system errors gracefully', async () => {
      mockFs.readFile.mockRejectedValue(new Error('Permission denied'));

      await expect(cli.importCredentials('test.json')).rejects.toThrow('Permission denied');
    });

    it('should handle malformed JSON gracefully', async () => {
      mockFs.readFile.mockResolvedValue('{"incomplete": json}');

      await expect(cli.importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should handle empty files', async () => {
      mockFs.readFile.mockResolvedValue('');

      await expect(cli.importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should handle null/undefined in credentials', async () => {
      const credentials = [
        null,
        {
          name: 'Valid Credential',
          baseUrl: 'https://test.atlassian.net'
        },
        undefined
      ];

      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      await expect(cli.importCredentials('test.json')).rejects.toThrow('Each credential must have at least name and baseUrl properties.');
    });
  });

  describe('Edge Cases', () => {
    it('should handle very large credential files', async () => {
      const largeCredentialArray = Array.from({ length: 1000 }, (_, i) => ({
        name: `Credential ${i}`,
        baseUrl: `https://test${i}.atlassian.net`,
        spaceKey: `TEST${i}`
      }));

      mockFs.readFile.mockResolvedValue(JSON.stringify(largeCredentialArray));

      const count = await cli.importCredentials('large.json');

      expect(count).toBe(1000);
    });

    it('should handle credentials with special characters', async () => {
      const credentials = [
        {
          name: 'Test Confluence with émojis 🚀',
          baseUrl: 'https://test-émojis.atlassian.net',
          spaceKey: 'TEST-🚀'
        }
      ];

      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await cli.importCredentials('special.json');

      expect(count).toBe(1);
    });

    it('should handle nested object structures', async () => {
      const data = {
        metadata: {
          version: '2.0',
          created: '2023-01-01'
        },
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            config: {
              timeout: 5000,
              retries: 3
            }
          }
        ]
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(data));

      const count = await cli.importCredentials('nested.json');

      expect(count).toBe(1);
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      await expect(cli.cleanup()).resolves.not.toThrow();
    });
  });
});
