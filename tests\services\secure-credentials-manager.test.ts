/**
 * Tests for SecureCredentialsManager
 */

import { SecureCredentialsManager } from '../../src/services/secure-credentials-manager';

// Mock dependencies
jest.mock('../../src/utils/crypto-utils');
jest.mock('../../src/utils/file-utils');
jest.mock('../../src/utils/logger');
jest.mock('../../src/config/app-config');

describe('SecureCredentialsManager', () => {
  let credentialsManager: SecureCredentialsManager;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    credentialsManager = new SecureCredentialsManager();
  });

  afterEach(async () => {
    // Cleanup
    try {
      await credentialsManager.cleanup();
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Constructor', () => {
    it('should create instance', () => {
      expect(credentialsManager).toBeInstanceOf(SecureCredentialsManager);
    });
  });

  describe('Basic Operations', () => {
    it('should start with empty credentials', () => {
      const credentials = credentialsManager.getCredentials();
      expect(credentials).toEqual([]);
    });

    it('should handle cleanup', async () => {
      await expect(credentialsManager.cleanup()).resolves.not.toThrow();
    });
  });
});
