/**
 * Tests for crypto utilities
 */

import { CryptoUtils } from '../../src/utils/crypto-utils';

describe('CryptoUtils', () => {
  describe('generateKey', () => {
    it('should generate a 32-byte key', () => {
      const key = CryptoUtils.generateKey();
      expect(key).toBeInstanceOf(Buffer);
      expect(key.length).toBe(32);
    });

    it('should generate different keys each time', () => {
      const key1 = CryptoUtils.generateKey();
      const key2 = CryptoUtils.generateKey();
      expect(key1.equals(key2)).toBe(false);
    });
  });

  describe('encryptData and decryptData', () => {
    it('should encrypt and decrypt data correctly', () => {
      const key = CryptoUtils.generateKey();
      const originalData = 'test data';

      const encrypted = CryptoUtils.encryptData(originalData, key);
      expect(encrypted).toHaveProperty('salt');
      expect(encrypted).toHaveProperty('iv');
      expect(encrypted).toHaveProperty('encryptedData');

      const decrypted = CryptoUtils.decryptData(encrypted, key);
      expect(decrypted).toBe(originalData);
    });

    it('should produce different encrypted data for same input', () => {
      const key = CryptoUtils.generateKey();
      const data = 'test data';

      const encrypted1 = CryptoUtils.encryptData(data, key);
      const encrypted2 = CryptoUtils.encryptData(data, key);

      expect(encrypted1.encryptedData).not.toBe(encrypted2.encryptedData);
      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      expect(encrypted1.salt).not.toBe(encrypted2.salt);
    });
  });



  describe('secureWipe', () => {
    it('should wipe buffer data', () => {
      const buffer = Buffer.from('sensitive data');
      CryptoUtils.secureWipe(buffer);

      // Check that buffer is filled with zeros
      for (let i = 0; i < buffer.length; i++) {
        expect(buffer[i]).toBe(0);
      }
    });

  });

  describe('Edge Cases', () => {
    it('should handle unicode strings', () => {
      const key = CryptoUtils.generateKey();
      const unicodeText = 'Hello 🌍 世界 🚀';

      const encrypted = CryptoUtils.encryptData(unicodeText, key);
      const decrypted = CryptoUtils.decryptData(encrypted, key);

      expect(decrypted).toBe(unicodeText);
    });

    it('should handle special characters in strings', () => {
      const key = CryptoUtils.generateKey();
      const specialText = 'Special chars: \n\t\r\0\x01\x02';

      const encrypted = CryptoUtils.encryptData(specialText, key);
      const decrypted = CryptoUtils.decryptData(encrypted, key);

      expect(decrypted).toBe(specialText);
    });

    it('should handle empty strings', () => {
      const key = CryptoUtils.generateKey();
      const emptyText = '';

      const encrypted = CryptoUtils.encryptData(emptyText, key);
      const decrypted = CryptoUtils.decryptData(encrypted, key);

      expect(decrypted).toBe(emptyText);
    });

    it('should handle very long strings', () => {
      const key = CryptoUtils.generateKey();
      const longText = 'a'.repeat(10000);

      const encrypted = CryptoUtils.encryptData(longText, key);
      const decrypted = CryptoUtils.decryptData(encrypted, key);

      expect(decrypted).toBe(longText);
    });
  });
});