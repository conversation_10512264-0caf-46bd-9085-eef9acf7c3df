import { AppError } from './app-error';

/**
 * Error thrown when authentication fails
 */
export class AuthenticationError extends AppError {
  readonly code = 'AUTH_ERROR';
  readonly exitCode = 1;
  
  constructor(
    message: string,
    public readonly userMessage: string = 'Authentication failed. Please check your credentials.',
    details?: Record<string, any>
  ) {
    super(message, details);
  }
  
  /**
   * Factory method for password-related authentication errors
   */
  static invalidPassword(message: string = 'Invalid password provided'): AuthenticationError {
    return new AuthenticationError(
      message,
      'The password you entered is incorrect. Please try again.'
    );
  }
  
  /**
   * Factory method for missing password errors
   */
  static passwordRequired(): AuthenticationError {
    return new AuthenticationError(
      'Password required to decrypt master key',
      'A password is required to decrypt your secure data. Please provide your password.'
    );
  }
  
  /**
   * Factory method for token-related authentication errors
   */
  static invalidToken(details?: Record<string, any>): AuthenticationError {
    return new AuthenticationError(
      'Invalid or expired authentication token',
      'Your authentication token is invalid or has expired. Please log in again.',
      details
    );
  }
  
  /**
   * Factory method for cookie-related authentication errors
   */
  static invalidSession(details?: Record<string, any>): AuthenticationError {
    return new AuthenticationError(
      'Invalid or expired session',
      'Your session has expired. Please log in again.',
      details
    );
  }
}