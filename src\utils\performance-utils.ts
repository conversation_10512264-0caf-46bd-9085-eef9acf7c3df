/**
 * Performance optimization utilities for lazy loading and resource management
 */

import { logger } from './logger';

export interface LazyModule<T> {
  load(): Promise<T>;
  isLoaded(): boolean;
  unload(): void;
}

export interface ResourceCleanup {
  cleanup(): Promise<void>;
}

export class PerformanceUtils {
  private static loadedModules = new Map<string, any>();
  private static cleanupTasks = new Set<ResourceCleanup>();

  /**
   * Create a lazy-loaded module wrapper
   */
  static createLazyModule<T>(
    moduleId: string,
    loader: () => Promise<T>,
    unloader?: (module: T) => Promise<void>
  ): LazyModule<T> {
    let loadPromise: Promise<T> | null = null;
    let loadedModule: T | null = null;

    return {
      async load(): Promise<T> {
        if (loadedModule) {
          return loadedModule;
        }

        if (loadPromise) {
          return loadPromise;
        }

        logger.debug(`Lazy loading module: ${moduleId}`);
        const startTime = Date.now();

        loadPromise = loader().then(module => {
          loadedModule = module;
          PerformanceUtils.loadedModules.set(moduleId, module);
          const loadTime = Date.now() - startTime;
          logger.debug(`Module ${moduleId} loaded in ${loadTime}ms`);
          return module;
        }).catch(error => {
          loadPromise = null;
          logger.error(`Failed to load module ${moduleId}:`, error);
          throw error;
        });

        return loadPromise;
      },

      isLoaded(): boolean {
        return loadedModule !== null;
      },

      unload(): void {
        if (loadedModule && unloader) {
          unloader(loadedModule).catch(error => {
            logger.error(`Error unloading module ${moduleId}:`, error);
          });
        }
        loadedModule = null;
        loadPromise = null;
        PerformanceUtils.loadedModules.delete(moduleId);
        logger.debug(`Module ${moduleId} unloaded`);
      }
    };
  }

  /**
   * Register a cleanup task
   */
  static registerCleanup(cleanup: ResourceCleanup): void {
    this.cleanupTasks.add(cleanup);
  }

  /**
   * Unregister a cleanup task
   */
  static unregisterCleanup(cleanup: ResourceCleanup): void {
    this.cleanupTasks.delete(cleanup);
  }

  /**
   * Execute all registered cleanup tasks
   */
  static async executeCleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.cleanupTasks).map(async (task) => {
      try {
        await task.cleanup();
      } catch (error) {
        logger.error('Error during cleanup:', error);
      }
    });

    await Promise.allSettled(cleanupPromises);
    this.cleanupTasks.clear();
    logger.debug('All cleanup tasks executed');
  }

  /**
   * Secure memory wipe for sensitive data
   */
  static secureWipe(data: any): void {
    if (Buffer.isBuffer(data)) {
      data.fill(0);
    } else if (typeof data === 'string') {
      // For strings, we can't directly wipe memory, but we can try to overwrite the reference
      data = '';
    } else if (Array.isArray(data)) {
      data.fill(null);
      data.length = 0;
    } else if (typeof data === 'object' && data !== null) {
      // Recursively wipe object properties
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          this.secureWipe(data[key]);
          delete data[key];
        }
      }
    }
  }

  /**
   * Create a timeout wrapper for operations
   */
  static withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
    timeoutMessage?: string
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(timeoutMessage || `Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      operation()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Debounce function calls
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    waitMs: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), waitMs);
    };
  }

  /**
   * Throttle function calls
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limitMs: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limitMs);
      }
    };
  }

  /**
   * Memory usage monitoring
   */
  static getMemoryUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  }

  /**
   * Log memory usage with context
   */
  static logMemoryUsage(context: string): void {
    const usage = this.getMemoryUsage();
    const formatBytes = (bytes: number) => (bytes / 1024 / 1024).toFixed(2) + ' MB';
    
    logger.debug(`Memory usage [${context}]:`, {
      rss: formatBytes(usage.rss),
      heapUsed: formatBytes(usage.heapUsed),
      heapTotal: formatBytes(usage.heapTotal),
      external: formatBytes(usage.external)
    });
  }

  /**
   * Force garbage collection if available
   */
  static forceGarbageCollection(): void {
    if (global.gc) {
      global.gc();
      logger.debug('Forced garbage collection');
    } else {
      logger.debug('Garbage collection not available (run with --expose-gc)');
    }
  }
}

/**
 * Setup process cleanup handlers
 */
export function setupProcessCleanup(): void {
  const cleanup = async () => {
    logger.info('Performing cleanup before exit...');
    await PerformanceUtils.executeCleanup();
    PerformanceUtils.forceGarbageCollection();
  };

  // Handle different exit scenarios
  process.on('exit', () => {
    // Synchronous cleanup only
    logger.debug('Process exiting...');
  });

  process.on('SIGINT', async () => {
    logger.info('Received SIGINT, cleaning up...');
    await cleanup();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, cleaning up...');
    await cleanup();
    process.exit(0);
  });

  process.on('uncaughtException', async (error) => {
    logger.error('Uncaught exception:', error);
    await cleanup();
    process.exit(1);
  });

  process.on('unhandledRejection', async (reason, promise) => {
    logger.error('Unhandled rejection at:', promise, 'reason:', reason);
    await cleanup();
    process.exit(1);
  });
}