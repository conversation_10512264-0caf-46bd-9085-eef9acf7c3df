/**
 * Tests for update-confluence-page-cli.ts
 */

// Mock dependencies
jest.mock('fs/promises');
jest.mock('marked');
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/services/secure-cookie-manager');
jest.mock('../../src/services/lazy-puppeteer-manager');
jest.mock('../../src/services/http-client');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger');
jest.mock('../../src/utils/performance-utils');

describe('update-confluence-page-cli', () => {
  // Mock process methods
  const mockExit = jest.spyOn(process, 'exit').mockImplementation(() => {
    throw new Error('process.exit called');
  });
  const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();
  const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();

  let originalArgv: string[];

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Store original values
    originalArgv = process.argv;
  });

  afterEach(() => {
    // Restore original values
    process.argv = originalArgv;
  });

  describe('Basic CLI Tests', () => {
    it('should be testable', () => {
      expect(true).toBe(true);
    });

    it('should parse command line arguments', () => {
      process.argv = [
        'node',
        'update-confluence-page-cli.js',
        '12345',
        'test.md',
        'Test Confluence Instance'
      ];

      const args = process.argv.slice(2);
      expect(args).toEqual(['12345', 'test.md', 'Test Confluence Instance']);
    });

    it('should handle missing arguments', () => {
      process.argv = ['node', 'update-confluence-page-cli.js'];

      const args = process.argv.slice(2);
      expect(args).toEqual([]);
    });

    it('should handle partial arguments', () => {
      process.argv = ['node', 'update-confluence-page-cli.js', '12345'];

      const args = process.argv.slice(2);
      expect(args).toEqual(['12345']);
    });

    it('should handle PowerShell special characters', () => {
      process.argv = [
        'node',
        'update-confluence-page-cli.js',
        '12345',
        'test.md',
        'Test^Confluence^Instance'
      ];

      const args = process.argv.slice(2);
      const instanceIdentifier = args[2];

      // Simulate PowerShell character removal
      const cleanedIdentifier = instanceIdentifier?.replace(/\^/g, '');
      expect(cleanedIdentifier).toBe('TestConfluenceInstance');
    });
  });

  describe('Argument Validation', () => {
    it('should validate page ID format', () => {
      const pageId = '12345';
      const isValidPageId = /^\d+$/.test(pageId);
      expect(isValidPageId).toBe(true);
    });

    it('should reject invalid page ID', () => {
      const pageId = 'invalid-id';
      const isValidPageId = /^\d+$/.test(pageId);
      expect(isValidPageId).toBe(false);
    });

    it('should validate file extension', () => {
      const filePath = 'test.md';
      const isMarkdownFile = filePath.endsWith('.md') || filePath.endsWith('.markdown');
      expect(isMarkdownFile).toBe(true);
    });

    it('should reject non-markdown files', () => {
      const filePath = 'test.txt';
      const isMarkdownFile = filePath.endsWith('.md') || filePath.endsWith('.markdown');
      expect(isMarkdownFile).toBe(false);
    });

    it('should handle empty page ID', () => {
      const pageId = '';
      const isValidPageId = pageId.trim().length > 0 && /^\d+$/.test(pageId);
      expect(isValidPageId).toBe(false);
    });

    it('should handle empty file path', () => {
      const filePath = '';
      const isValidFilePath = filePath.trim().length > 0;
      expect(isValidFilePath).toBe(false);
    });
  });

  describe('URL Processing', () => {
    it('should extract domain from base URL', () => {
      const baseUrl = 'https://test.atlassian.net';
      const url = new URL(baseUrl);
      expect(url.hostname).toBe('test.atlassian.net');
    });

    it('should handle URL with path', () => {
      const baseUrl = 'https://test.atlassian.net/wiki';
      const url = new URL(baseUrl);
      expect(url.hostname).toBe('test.atlassian.net');
      expect(url.pathname).toBe('/wiki');
    });

    it('should validate URL format', () => {
      const baseUrl = 'https://test.atlassian.net';
      let isValidUrl = false;

      try {
        new URL(baseUrl);
        isValidUrl = true;
      } catch (error) {
        isValidUrl = false;
      }

      expect(isValidUrl).toBe(true);
    });

    it('should reject invalid URL', () => {
      const baseUrl = 'invalid-url';
      let isValidUrl = false;

      try {
        new URL(baseUrl);
        isValidUrl = true;
      } catch (error) {
        isValidUrl = false;
      }

      expect(isValidUrl).toBe(false);
    });
  });

  describe('HTML Processing', () => {
    it('should convert self-closing tags', () => {
      const html = '<p>Test</p><br><hr>';
      const processedHtml = html
        .replace(/<br>/g, '<br />')
        .replace(/<hr>/g, '<hr />');

      expect(processedHtml).toBe('<p>Test</p><br /><hr />');
    });

    it('should handle img tags', () => {
      const html = '<img src="test.jpg">';
      const processedHtml = html.replace(/<img([^>]*)>/g, '<img$1 />');

      expect(processedHtml).toBe('<img src="test.jpg" />');
    });

    it('should handle input tags', () => {
      const html = '<input type="text">';
      const processedHtml = html.replace(/<input([^>]*)>/g, '<input$1 />');

      expect(processedHtml).toBe('<input type="text" />');
    });

    it('should preserve already closed tags', () => {
      const html = '<p>Test</p><br /><hr />';
      const processedHtml = html
        .replace(/<br>/g, '<br />')
        .replace(/<hr>/g, '<hr />');

      expect(processedHtml).toBe('<p>Test</p><br /><hr />');
    });
  });

  describe('Credential Matching', () => {
    it('should match by exact name', () => {
      const credentials = [
        { name: 'Test Confluence', baseUrl: 'https://test.atlassian.net' },
        { name: 'Other Confluence', baseUrl: 'https://other.atlassian.net' }
      ];

      const identifier = 'Test Confluence';
      const found = credentials.find(cred => cred.name === identifier);

      expect(found?.name).toBe('Test Confluence');
    });

    it('should match by base URL', () => {
      const credentials = [
        { name: 'Test Confluence', baseUrl: 'https://test.atlassian.net' },
        { name: 'Other Confluence', baseUrl: 'https://other.atlassian.net' }
      ];

      const identifier = 'https://test.atlassian.net';
      const found = credentials.find(cred => cred.baseUrl === identifier);

      expect(found?.name).toBe('Test Confluence');
    });

    it('should handle case-insensitive matching', () => {
      const credentials = [
        { name: 'Test Confluence', baseUrl: 'https://test.atlassian.net' }
      ];

      const identifier = 'test confluence';
      const found = credentials.find(cred =>
        cred.name.toLowerCase() === identifier.toLowerCase()
      );

      expect(found?.name).toBe('Test Confluence');
    });

    it('should return undefined for no match', () => {
      const credentials = [
        { name: 'Test Confluence', baseUrl: 'https://test.atlassian.net' }
      ];

      const identifier = 'Nonexistent';
      const found = credentials.find(cred => cred.name === identifier);

      expect(found).toBeUndefined();
    });
  });

  describe('Error Scenarios', () => {
    it('should handle empty credentials array', () => {
      const credentials: any[] = [];
      const identifier = 'Test Confluence';
      const found = credentials.find(cred => cred.name === identifier);

      expect(found).toBeUndefined();
    });

    it('should handle missing space key', () => {
      const credential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: undefined as string | undefined
      };

      const hasSpaceKey = credential.spaceKey !== undefined && credential.spaceKey.trim().length > 0;
      expect(hasSpaceKey).toBe(false);
    });

    it('should handle missing token', () => {
      const credential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: undefined as string | undefined,
        puppeteerLogin: false
      };

      const hasToken = credential.token !== undefined && credential.token.trim().length > 0;
      expect(hasToken).toBe(false);
    });

    it('should validate authentication method', () => {
      const credential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: undefined as string | undefined,
        puppeteerLogin: true
      };

      const hasValidAuth = credential.puppeteerLogin || (credential.token && credential.token.trim().length > 0);
      expect(hasValidAuth).toBe(true);
    });
  });

  describe('File Path Processing', () => {
    it('should handle relative paths', () => {
      const filePath = './test.md';
      const isRelative = !filePath.startsWith('/') && !filePath.match(/^[A-Za-z]:/);
      expect(isRelative).toBe(true);
    });

    it('should handle absolute paths', () => {
      const filePath = '/home/<USER>/test.md';
      const isAbsolute = filePath.startsWith('/');
      expect(isAbsolute).toBe(true);
    });

    it('should handle Windows paths', () => {
      const filePath = 'C:\\Users\\<USER>