/**
 * General application error class
 */

import { AppError } from './app-error';

export class ApplicationError extends AppError {
  readonly code = 'APPLICATION_ERROR';
  readonly userMessage: string;
  readonly exitCode = 1;
  
  constructor(message: string, userMessage: string, details?: Record<string, any>) {
    super(message, details);
    this.userMessage = userMessage;
  }
  
  static unexpected(error: Error, details?: Record<string, any>): ApplicationError {
    return new ApplicationError(
      `Unexpected error: ${error.message}`,
      'An unexpected error occurred. Please check the logs for more details.',
      {
        originalError: error.message,
        stack: error.stack,
        ...details
      }
    );
  }
  
  static notImplemented(feature: string): ApplicationError {
    return new ApplicationError(
      `Not implemented: ${feature}`,
      `The feature '${feature}' is not yet implemented.`,
      { feature }
    );
  }
  
  static configurationError(message: string, details?: Record<string, any>): ApplicationError {
    return new ApplicationError(
      `Configuration error: ${message}`,
      `There is a configuration error: ${message}`,
      details
    );
  }
}