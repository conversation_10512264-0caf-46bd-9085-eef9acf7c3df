import { SecureCredentialsManager, ConfluenceCredential } from '../services/secure-credentials-manager';
import { SecureCookieManager, EnhancedCookie } from '../services/secure-cookie-manager';
import { LazyPuppeteerManager } from '../services/lazy-puppeteer-manager';
import { ValidationError, NetworkError, AuthenticationError } from '../errors';
import { logger } from '../utils/logger';
import { PerformanceUtils } from '../utils/performance-utils';

// Use any types to avoid complex Puppeteer type conflicts
type PuppeteerBrowser = any;
type PuppeteerPage = any;

/**
 * Manager class for Confluence operations
 */
export class ConfluenceManager {
    private credentialsManager: SecureCredentialsManager;
    private cookieManager: SecureCookieManager;
    private puppeteerManager: LazyPuppeteerManager;
    private baseUrl: string;

    /**
     * Creates a new ConfluenceManager instance
     * 
     * @param baseUrl - The base URL for the Confluence instance
     * @param credentialsManager - The credentials manager instance
     * @param cookieManager - The cookie manager instance
     * @param puppeteerManager - The puppeteer manager instance
     */
    constructor(
        baseUrl: string,
        credentialsManager: SecureCredentialsManager,
        cookieManager: SecureCookieManager,
        puppeteerManager: LazyPuppeteerManager
    ) {
        if (!baseUrl) {
            throw new ValidationError(
                'Base URL is required',
                'A base URL for the Confluence instance is required'
            );
        }

        this.baseUrl = baseUrl;
        this.credentialsManager = credentialsManager;
        this.cookieManager = cookieManager;
        this.puppeteerManager = puppeteerManager;

        logger.debug('ConfluenceManager initialized');
    }

    /**
     * Updates a Confluence page with new content
     * 
     * @param pageId - The ID of the page to update
     * @param content - The new content for the page
     * @param title - Optional new title for the page
     * @returns Promise resolving to the updated page ID
     */
    async updatePage(pageId: string, content: string, title?: string): Promise<string> {
        try {
            logger.info(`Updating Confluence page: ${pageId}`);

            // Ensure we have valid authentication
            await this.ensureAuthentication();

            // Get the browser instance
            const browser = await this.puppeteerManager.launchBrowser();
            const page = await this.puppeteerManager.createPage(browser);

            try {
                // Load cookies for authentication
                const cookies = this.cookieManager.getCookiesForBaseUrl(this.baseUrl);
                await page.setCookie(...cookies);

                // Navigate to the edit page
                const editUrl = `${this.baseUrl}/pages/editpage.action?pageId=${pageId}`;
                await this.puppeteerManager.navigateToUrl(page, editUrl);

                // Check if we're still on the login page
                if ((await page.url()).includes('login')) {
                    throw new AuthenticationError(
                        'Authentication failed - redirected to login page',
                        'Authentication failed. Please check your credentials.'
                    );
                }

                // Update the page content
                await page.evaluate((newContent: string) => {
                    // This assumes a modern Confluence editor
                    const editor = document.querySelector('.confluence-editor');
                    if (editor) {
                        // Implementation would depend on the specific Confluence version
                        console.log('Setting content in editor');
                    }
                }, content);

                // Update title if provided
                if (title) {
                    await page.evaluate((newTitle: string) => {
                        const titleField = document.querySelector('#title-text');
                        if (titleField) {
                            (titleField as HTMLInputElement).value = newTitle;
                        }
                    }, title);
                }

                // Submit the form
                await Promise.all([
                    page.click('#save-button'),
                    page.waitForNavigation({ waitUntil: 'networkidle2' })
                ]);

                logger.info('Page updated successfully');
                return pageId;
            } finally {
                await this.puppeteerManager.closePage(page);
                await this.puppeteerManager.closeBrowser(browser);
            }
        } catch (error: unknown) {
            if (error instanceof AuthenticationError) {
                throw error;
            } else if (error instanceof Error && error.name === 'TimeoutError') {
                throw new NetworkError(
                    'Operation timed out while updating the page',
                    'The operation timed out. Please check your network connection and try again.'
                );
            } else {
                logger.error('Failed to update page', error);
                throw error;
            }
        }
    }

    /**
     * Ensures that we have valid authentication for Confluence
     */
    private async ensureAuthentication(): Promise<void> {
        // Check if we have valid cookies
        const hasCookies = this.cookieManager.hasCookiesForBaseUrl(this.baseUrl);

        if (!hasCookies) {
            logger.info('No valid cookies found, performing authentication');
            await this.authenticate();
        } else {
            logger.debug('Using existing authentication cookies');
        }
    }

    /**
     * Authenticates with Confluence using stored credentials
     */
    private async authenticate(): Promise<void> {
        const credential = this.credentialsManager.getCredentialByBaseUrl(this.baseUrl);

        if (!credential || !credential.username || !credential.password) {
            throw new AuthenticationError(
                'No valid credentials found',
                'No valid credentials found for this Confluence instance.'
            );
        }

        const browser = await this.puppeteerManager.launchBrowser();
        const page = await this.puppeteerManager.createPage(browser);

        try {
            // Navigate to login page
            await this.puppeteerManager.navigateToUrl(page, `${this.baseUrl}/login.action`);

            // Fill in login form
            await page.type('#username', credential.username);
            await page.type('#password', credential.password);

            // Submit form and wait for navigation
            await Promise.all([
                page.click('#login-submit'),
                page.waitForNavigation({ waitUntil: 'networkidle2' })
            ]);

            // Check if login was successful
            if ((await page.url()).includes('login')) {
                throw new AuthenticationError(
                    'Login failed - still on login page',
                    'Login failed. Please check your credentials.'
                );
            }

            // Save cookies for future requests
            const cookies = await page.cookies();
            await this.cookieManager.saveCookies(cookies, this.baseUrl);

            logger.info('Authentication successful');
        } catch (error: unknown) {
            if (error instanceof AuthenticationError) {
                throw error;
            } else {
                logger.error('Authentication failed', error);
                throw new AuthenticationError(
                    'Failed to authenticate with Confluence',
                    'Authentication failed. Please check your network connection and credentials.'
                );
            }
        } finally {
            await this.puppeteerManager.closePage(page);
            await this.puppeteerManager.closeBrowser(browser);
        }
    }
}