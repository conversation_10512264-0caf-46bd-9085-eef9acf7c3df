/**
 * Tests for CLI utilities
 */

import { <PERSON><PERSON><PERSON>ars<PERSON>, CliCommand, CliO<PERSON>, STANDARD_OPTIONS, handleStandardCliFlow, EXIT_CODES, gracefulExit } from '../../src/utils/cli-utils';

describe('CliParser', () => {
  const testCommand: CliCommand = {
    name: 'test-cli',
    description: 'Test CLI command',
    usage: 'test-cli [options] <file>',
    version: '1.0.0',
    options: [
      {
        short: 'f',
        long: 'file',
        description: 'Input file',
        hasValue: true,
        required: true
      },
      {
        short: 'v',
        long: 'verbose',
        description: 'Verbose output',
        hasValue: false
      },
      {
        short: 'o',
        long: 'output',
        description: 'Output file',
        hasValue: true
      }
    ],
    examples: [
      'test-cli --file input.txt',
      'test-cli -f input.txt -v'
    ]
  };

  let parser: CliParser;
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    parser = new CliParser(testCommand);
    consoleSpy = jest.spyOn(console, 'log').mockImplementation();
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  describe('parse', () => {
    it('should parse long options with values', () => {
      const result = parser.parse(['--file', 'test.txt', '--output', 'out.txt']);
      expect(result.values.file).toBe('test.txt');
      expect(result.values.output).toBe('out.txt');
      expect(result.flags.verbose).toBeUndefined();
    });

    it('should parse short options with values', () => {
      const result = parser.parse(['-f', 'test.txt', '-o', 'out.txt']);
      expect(result.values.file).toBe('test.txt');
      expect(result.values.output).toBe('out.txt');
    });

    it('should parse combined short options', () => {
      const result = parser.parse(['-ftest.txt', '-v']);
      expect(result.values.file).toBe('test.txt');
      expect(result.flags.verbose).toBe(true);
    });

    it('should parse flags without values', () => {
      const result = parser.parse(['--verbose']);
      expect(result.flags.verbose).toBe(true);
    });

    it('should parse help flag', () => {
      const result1 = parser.parse(['--help']);
      expect(result1.help).toBe(true);

      const result2 = parser.parse(['-h']);
      expect(result2.help).toBe(true);
    });

    it('should parse version flag', () => {
      const result1 = parser.parse(['--version']);
      expect(result1.version).toBe(true);

      const result2 = parser.parse(['-V']);
      expect(result2.version).toBe(true);
    });

    it('should collect positional arguments', () => {
      const result = parser.parse(['--file', 'test.txt', 'pos1', 'pos2']);
      expect(result.positional).toEqual(['pos1', 'pos2']);
    });

    it('should throw error for unknown long option', () => {
      expect(() => parser.parse(['--unknown'])).toThrow('Unknown option: --unknown');
    });

    it('should throw error for unknown short option', () => {
      expect(() => parser.parse(['-x'])).toThrow('Unknown option: -x');
    });

    it('should throw error for option requiring value without value', () => {
      expect(() => parser.parse(['--file'])).toThrow('Option --file requires a value');
      expect(() => parser.parse(['-f'])).toThrow('Option -f requires a value');
    });

    it('should handle mixed arguments correctly', () => {
      const result = parser.parse(['-v', '--file', 'test.txt', 'positional', '--output', 'out.txt']);
      expect(result.flags.verbose).toBe(true);
      expect(result.values.file).toBe('test.txt');
      expect(result.values.output).toBe('out.txt');
      expect(result.positional).toEqual(['positional']);
    });
  });

  describe('showHelp', () => {
    it('should display help information', () => {
      parser.showHelp();

      expect(consoleSpy).toHaveBeenCalledWith('test-cli - Test CLI command');
      expect(consoleSpy).toHaveBeenCalledWith('Usage: test-cli [options] <file>');
      expect(consoleSpy).toHaveBeenCalledWith('Options:');
      expect(consoleSpy).toHaveBeenCalledWith('Examples:');
    });

    it('should display options with proper formatting', () => {
      parser.showHelp();

      const calls = consoleSpy.mock.calls.map(call => call[0]);
      const optionCalls = calls.filter(call => call.includes('-f, --file'));
      expect(optionCalls.length).toBeGreaterThan(0);
    });

    it('should display examples', () => {
      parser.showHelp();

      const calls = consoleSpy.mock.calls.map(call => call[0]);
      expect(calls.some(call => call.includes('test-cli --file input.txt'))).toBe(true);
    });
  });

  describe('showVersion', () => {
    it('should display version with command name', () => {
      parser.showVersion();
      expect(consoleSpy).toHaveBeenCalledWith('test-cli v1.0.0');
    });

    it('should display only command name when no version', () => {
      const commandWithoutVersion = { ...testCommand, version: undefined };
      const parserWithoutVersion = new CliParser(commandWithoutVersion);

      parserWithoutVersion.showVersion();
      expect(consoleSpy).toHaveBeenCalledWith('test-cli');
    });
  });

  describe('validateRequired', () => {
    it('should pass validation when all required options are provided', () => {
      const parsed = parser.parse(['--file', 'test.txt']);
      expect(() => parser.validateRequired(parsed)).not.toThrow();
    });

    it('should throw error when required options are missing', () => {
      const parsed = parser.parse(['--verbose']);
      expect(() => parser.validateRequired(parsed)).toThrow('Missing required options: --file');
    });

    it('should handle multiple missing required options', () => {
      const commandWithMultipleRequired: CliCommand = {
        ...testCommand,
        options: [
          ...testCommand.options,
          {
            short: 'r',
            long: 'required2',
            description: 'Another required option',
            hasValue: true,
            required: true
          }
        ]
      };

      const parserWithMultiple = new CliParser(commandWithMultipleRequired);
      const parsed = parserWithMultiple.parse(['--verbose']);

      expect(() => parserWithMultiple.validateRequired(parsed)).toThrow('Missing required options: --file, --required2');
    });
  });
});

describe('STANDARD_OPTIONS', () => {
  it('should contain standard CLI options', () => {
    expect(STANDARD_OPTIONS).toHaveLength(4);

    const optionLongs = STANDARD_OPTIONS.map(opt => opt.long);
    expect(optionLongs).toContain('help');
    expect(optionLongs).toContain('version');
    expect(optionLongs).toContain('verbose');
    expect(optionLongs).toContain('quiet');
  });

  it('should have correct short options', () => {
    const helpOption = STANDARD_OPTIONS.find(opt => opt.long === 'help');
    expect(helpOption?.short).toBe('h');

    const versionOption = STANDARD_OPTIONS.find(opt => opt.long === 'version');
    expect(versionOption?.short).toBe('V');
  });
});

describe('handleStandardCliFlow', () => {
  const testCommand: CliCommand = {
    name: 'test-cli',
    description: 'Test CLI command',
    usage: 'test-cli [options] <file>',
    version: '1.0.0',
    options: [
      {
        short: 'f',
        long: 'file',
        description: 'Input file',
        hasValue: true,
        required: true
      },
      {
        short: 'v',
        long: 'verbose',
        description: 'Verbose output',
        hasValue: false
      }
    ],
    examples: ['test-cli --file input.txt']
  };

  let parser: CliParser;
  let consoleSpy: jest.SpyInstance;
  let processExitSpy: jest.SpyInstance;

  beforeEach(() => {
    parser = new CliParser(testCommand);
    consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    processExitSpy = jest.spyOn(process, 'exit').mockImplementation();
  });

  afterEach(() => {
    consoleSpy.mockRestore();
    processExitSpy.mockRestore();
    jest.restoreAllMocks();
  });

  it('should handle help flag and return true', () => {
    const parsed = parser.parse(['--help']);
    const result = handleStandardCliFlow(parser, parsed);

    expect(result).toBe(true);
    expect(consoleSpy).toHaveBeenCalled();
  });

  it('should handle version flag and return true', () => {
    const parsed = parser.parse(['--version']);
    const result = handleStandardCliFlow(parser, parsed);

    expect(result).toBe(true);
    expect(consoleSpy).toHaveBeenCalledWith('test-cli v1.0.0');
  });

  it('should validate required options and return false when valid', () => {
    const parsed = parser.parse(['--file', 'test.txt']);
    const result = handleStandardCliFlow(parser, parsed);

    expect(result).toBe(false);
  });

  it('should handle validation errors with custom error handler', () => {
    const errorHandler = jest.fn();
    const parsed = parser.parse(['--verbose']); // Missing required --file

    const result = handleStandardCliFlow(parser, parsed, errorHandler);

    expect(result).toBe(true);
    expect(errorHandler).toHaveBeenCalled();
    expect(processExitSpy).not.toHaveBeenCalled();
  });

  it('should handle validation errors with default error handler', () => {
    const parsed = parser.parse(['--verbose']); // Missing required --file

    const result = handleStandardCliFlow(parser, parsed);

    expect(result).toBe(true);
    expect(processExitSpy).toHaveBeenCalledWith(1);
  });
});

describe('EXIT_CODES', () => {
  it('should have correct exit codes', () => {
    expect(EXIT_CODES.SUCCESS).toBe(0);
    expect(EXIT_CODES.GENERAL_ERROR).toBe(1);
    expect(EXIT_CODES.SYSTEM_ERROR).toBe(2);
    expect(EXIT_CODES.APPLICATION_ERROR).toBe(3);
    expect(EXIT_CODES.USER_CANCELLED).toBe(130);
  });
});

describe('gracefulExit', () => {
  let consoleSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;
  let processExitSpy: jest.SpyInstance;

  beforeEach(() => {
    consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    processExitSpy = jest.spyOn(process, 'exit').mockImplementation();
  });

  afterEach(() => {
    consoleSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    processExitSpy.mockRestore();
  });

  it('should exit with success code and log message', () => {
    gracefulExit(EXIT_CODES.SUCCESS, 'Operation completed');

    expect(consoleSpy).toHaveBeenCalledWith('Operation completed');
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });

  it('should exit with error code and log error message', () => {
    gracefulExit(EXIT_CODES.GENERAL_ERROR, 'Operation failed');

    expect(consoleErrorSpy).toHaveBeenCalledWith('Operation failed');
    expect(processExitSpy).toHaveBeenCalledWith(1);
  });

  it('should execute cleanup function before exit', () => {
    const cleanup = jest.fn();
    gracefulExit(EXIT_CODES.SUCCESS, undefined, cleanup);

    expect(cleanup).toHaveBeenCalled();
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });

  it('should handle cleanup errors gracefully', () => {
    const cleanup = jest.fn(() => {
      throw new Error('Cleanup failed');
    });

    gracefulExit(EXIT_CODES.SUCCESS, undefined, cleanup);

    expect(cleanup).toHaveBeenCalled();
    expect(consoleErrorSpy).toHaveBeenCalledWith('Error during cleanup:', 'Cleanup failed');
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });

  it('should exit without message when none provided', () => {
    gracefulExit(EXIT_CODES.SUCCESS);

    expect(consoleSpy).not.toHaveBeenCalled();
    expect(consoleErrorSpy).not.toHaveBeenCalled();
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });
});
