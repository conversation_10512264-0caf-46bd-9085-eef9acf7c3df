# HTTP User-Agent Vereinheitlichung

## Übersicht

Dieses Dokument beschreibt die Implementierung einer einheitlichen HTTP User-Agent-Konfiguration für das Confluence Page Update Tool.

## Implementierte Änderungen

### 1. Zentrale Konfiguration (`src/config/app-config.ts`)

- **Neue HttpConfig-Schnittstelle** hinzugefügt mit `userAgent` und `timeout` Eigenschaften
- **Standardkonfiguration** erweitert um HTTP-Einstellungen:
  ```typescript
  http: {
    userAgent: 'confluence-upload/1.0.0 (Node.js; Confluence Page Updater)',
    timeout: 30000 // 30 seconds
  }
  ```

### 2. Zentraler HTTP-Client (`src/services/http-client.ts`)

- **Neue HTTP-Client-Service** erstellt mit einheitlichem User-Agent
- **Axios-Instanzen** mit vorkonfigurierten Headern und Interceptors
- **S<PERSON>zielle Confluence-HTTP-Client** Funktion für API-Aufrufe
- **Logging-Integration** für Request/Response-Überwachung

### 3. Puppeteer-Integration (`src/services/lazy-puppeteer-manager.ts`)

- **User-Agent aus zentraler Konfiguration** verwendet statt hardcodiertem Wert
- **Browser-Argumente** aktualisiert: `--user-agent=${defaultConfig.http.userAgent}`

### 4. CLI-Integration (`src/cli/update-confluence-page-cli.ts`)

- **Axios-Import** ersetzt durch `createConfluenceHttpClient`
- **makeApiCall-Funktion** aktualisiert zur Verwendung des zentralen HTTP-Clients
- **Einheitlicher User-Agent** für alle API-Aufrufe

## Vorteile der Vereinheitlichung

### 1. Konsistenz
- Alle HTTP-Requests verwenden denselben User-Agent
- Einheitliche Identifikation gegenüber Confluence-Servern

### 2. Wartbarkeit
- Zentrale Konfiguration ermöglicht einfache Änderungen
- User-Agent kann an einer Stelle für das gesamte Tool aktualisiert werden

### 3. Debugging
- Einheitliche Logging-Funktionalität für alle HTTP-Requests
- Bessere Nachverfolgbarkeit von API-Aufrufen

### 4. Flexibilität
- Konfigurierbare Timeouts
- Erweiterbare Header-Konfiguration
- Spezielle Clients für verschiedene Anwendungsfälle

## Verwendung

### Standard HTTP-Client
```typescript
import { httpClient } from '../services/http-client';

const response = await httpClient.get('/api/endpoint');
```

### Confluence-spezifischer Client
```typescript
import { createConfluenceHttpClient } from '../services/http-client';

const client = createConfluenceHttpClient('https://confluence.example.com');
const response = await client.get('/rest/api/content/123');
```

### Benutzerdefinierter Client
```typescript
import { createHttpClient } from '../services/http-client';

const client = createHttpClient('https://api.example.com', {
  headers: {
    'Custom-Header': 'value'
  }
});
```

## Konfiguration

Der User-Agent kann in `src/config/app-config.ts` angepasst werden:

```typescript
http: {
  userAgent: 'your-custom-user-agent/1.0.0',
  timeout: 30000
}
```

## Betroffene Komponenten

1. **Puppeteer Browser**: Verwendet den konfigurierten User-Agent für alle Browser-Requests
2. **Confluence API**: Alle REST-API-Aufrufe verwenden den einheitlichen User-Agent
3. **HTTP-Interceptors**: Logging und Error-Handling mit User-Agent-Informationen

## Migration

Bestehender Code, der direkt `axios` verwendet, sollte auf die neuen HTTP-Client-Funktionen migriert werden:

**Vorher:**
```typescript
import axios from 'axios';
const response = await axios.get(url, { headers: { ... } });
```

**Nachher:**
```typescript
import { createConfluenceHttpClient } from '../services/http-client';
const client = createConfluenceHttpClient(baseUrl);
const response = await client.get(url);
```

## Kompatibilität

- Alle bestehenden API-Aufrufe funktionieren weiterhin
- Keine Breaking Changes für externe Schnittstellen
- Rückwärtskompatible Konfiguration
