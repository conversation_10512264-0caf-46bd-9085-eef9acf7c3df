/**
 * Tests for Logger utility
 */

import { Logger, LogLevel } from '../../src/utils/logger';
import { logger } from '../../src/utils/logger';

// Mock console methods
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();

describe('Logger', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    mockConsoleError.mockRestore();
    mockConsoleWarn.mockRestore();
    mockConsoleLog.mockRestore();
  });

  describe('Logger Class', () => {
    it('should create logger with default config', () => {
      const testLogger = new Logger();

      testLogger.info('test message');
      expect(mockConsoleLog).toHaveBeenCalledWith('test message');
    });

    it('should create logger with custom config', () => {
      const testLogger = new Logger({ level: 'debug', prefix: 'TEST' });

      testLogger.debug('debug message');
      expect(mockConsoleLog).toHaveBeenCalledWith('[DEBUG] [TEST] debug message');
    });

    it('should set log level', () => {
      const testLogger = new Logger({ level: 'info' });

      testLogger.debug('should not log');
      expect(mockConsoleLog).not.toHaveBeenCalled();

      testLogger.setLevel('debug');
      testLogger.debug('should log now');
      expect(mockConsoleLog).toHaveBeenCalledWith('[DEBUG] should log now');
    });

    it('should set prefix', () => {
      const testLogger = new Logger();

      testLogger.info('without prefix');
      expect(mockConsoleLog).toHaveBeenCalledWith('without prefix');

      testLogger.setPrefix('PREFIX');
      testLogger.info('with prefix');
      expect(mockConsoleLog).toHaveBeenCalledWith('[PREFIX] with prefix');
    });

    describe('Log Levels', () => {
      it('should respect silent level', () => {
        const testLogger = new Logger({ level: 'silent' });

        testLogger.error('error message');
        testLogger.warn('warn message');
        testLogger.info('info message');
        testLogger.debug('debug message');
        testLogger.verbose('verbose message');

        expect(mockConsoleError).not.toHaveBeenCalled();
        expect(mockConsoleWarn).not.toHaveBeenCalled();
        expect(mockConsoleLog).not.toHaveBeenCalled();
      });

      it('should respect error level', () => {
        const testLogger = new Logger({ level: 'error' });

        testLogger.error('error message');
        testLogger.warn('warn message');
        testLogger.info('info message');

        expect(mockConsoleError).toHaveBeenCalledWith('error message');
        expect(mockConsoleWarn).not.toHaveBeenCalled();
        expect(mockConsoleLog).not.toHaveBeenCalled();
      });

      it('should respect warn level', () => {
        const testLogger = new Logger({ level: 'warn' });

        testLogger.error('error message');
        testLogger.warn('warn message');
        testLogger.info('info message');

        expect(mockConsoleError).toHaveBeenCalledWith('error message');
        expect(mockConsoleWarn).toHaveBeenCalledWith('warn message');
        expect(mockConsoleLog).not.toHaveBeenCalled();
      });

      it('should respect info level', () => {
        const testLogger = new Logger({ level: 'info' });

        testLogger.error('error message');
        testLogger.warn('warn message');
        testLogger.info('info message');
        testLogger.debug('debug message');

        expect(mockConsoleError).toHaveBeenCalledWith('error message');
        expect(mockConsoleWarn).toHaveBeenCalledWith('warn message');
        expect(mockConsoleLog).toHaveBeenCalledWith('info message');
        expect(mockConsoleLog).toHaveBeenCalledTimes(1); // debug should not log
      });

      it('should respect debug level', () => {
        const testLogger = new Logger({ level: 'debug' });

        testLogger.info('info message');
        testLogger.debug('debug message');
        testLogger.verbose('verbose message');

        expect(mockConsoleLog).toHaveBeenCalledWith('info message');
        expect(mockConsoleLog).toHaveBeenCalledWith('[DEBUG] debug message');
        expect(mockConsoleLog).toHaveBeenCalledTimes(2); // verbose should not log
      });

      it('should respect verbose level', () => {
        const testLogger = new Logger({ level: 'verbose' });

        testLogger.debug('debug message');
        testLogger.verbose('verbose message');

        expect(mockConsoleLog).toHaveBeenCalledWith('[DEBUG] debug message');
        expect(mockConsoleLog).toHaveBeenCalledWith('[VERBOSE] verbose message');
        expect(mockConsoleLog).toHaveBeenCalledTimes(2);
      });
    });

    it('should call all log methods', () => {
      const testLogger = new Logger({ level: 'verbose' });

      testLogger.error('error message');
      testLogger.warn('warn message');
      testLogger.info('info message');
      testLogger.debug('debug message');
      testLogger.verbose('verbose message');

      expect(mockConsoleError).toHaveBeenCalledWith('error message');
      expect(mockConsoleWarn).toHaveBeenCalledWith('warn message');
      expect(mockConsoleLog).toHaveBeenCalledWith('info message');
      expect(mockConsoleLog).toHaveBeenCalledWith('[DEBUG] debug message');
      expect(mockConsoleLog).toHaveBeenCalledWith('[VERBOSE] verbose message');
    });
  });

  describe('Default Logger Instance', () => {
    it('should use default logger instance', () => {
      logger.info('test message from default logger');
      expect(mockConsoleLog).toHaveBeenCalledWith('test message from default logger');
    });

    it('should allow setting level on default logger', () => {
      logger.setLevel('debug');
      logger.debug('debug message');
      expect(mockConsoleLog).toHaveBeenCalledWith('[DEBUG] debug message');

      // Reset to default
      logger.setLevel('info');
    });

    it('should allow setting prefix on default logger', () => {
      logger.setPrefix('DEFAULT');
      logger.info('prefixed message');
      expect(mockConsoleLog).toHaveBeenCalledWith('[DEFAULT] prefixed message');

      // Reset prefix
      logger.setPrefix('');
    });
  });
});
