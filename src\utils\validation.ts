/**
 * Input validation utilities for credentials and configuration
 */

import { ConfluenceCredential } from '../types';
import { messages } from '../config/messages';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export class ValidationUtils {
  /**
   * Validate URL format
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate Confluence base URL
   */
  static isValidConfluenceUrl(baseUrl: string): ValidationResult {
    const errors: string[] = [];

    if (!baseUrl || typeof baseUrl !== 'string') {
      errors.push('Base URL is required');
      return { isValid: false, errors };
    }

    if (!this.isValidUrl(baseUrl)) {
      errors.push('Base URL must be a valid URL');
    }

    if (!baseUrl.startsWith('https://') && !baseUrl.startsWith('http://')) {
      errors.push('Base URL must start with http:// or https://');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate space key format
   */
  static isValidSpaceKey(spaceKey: string): ValidationResult {
    const errors: string[] = [];

    if (!spaceKey || typeof spaceKey !== 'string') {
      errors.push('Space key is required');
      return { isValid: false, errors };
    }

    // Confluence space keys typically contain only alphanumeric characters and underscores
    if (!/^[A-Za-z0-9_]+$/.test(spaceKey)) {
      errors.push('Space key should contain only letters, numbers, and underscores');
    }

    if (spaceKey.length < 1 || spaceKey.length > 255) {
      errors.push('Space key must be between 1 and 255 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate credential name
   */
  static isValidCredentialName(name: string): ValidationResult {
    const errors: string[] = [];

    if (!name || typeof name !== 'string') {
      errors.push('Credential name is required');
      return { isValid: false, errors };
    }

    if (name.trim().length === 0) {
      errors.push('Credential name cannot be empty');
    }

    if (name.length > 100) {
      errors.push('Credential name must be 100 characters or less');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate API token format
   */
  static isValidApiToken(token: string): ValidationResult {
    const errors: string[] = [];

    if (!token || typeof token !== 'string') {
      errors.push('API token is required');
      return { isValid: false, errors };
    }

    if (token.trim().length === 0) {
      errors.push('API token cannot be empty');
    }

    // Basic length check - Atlassian tokens are typically longer
    if (token.length < 10) {
      errors.push('API token appears to be too short');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate username format
   */
  static isValidUsername(username: string): ValidationResult {
    const errors: string[] = [];

    if (!username || typeof username !== 'string') {
      errors.push('Username is required');
      return { isValid: false, errors };
    }

    if (username.trim().length === 0) {
      errors.push('Username cannot be empty');
    }

    // Basic email format check if it looks like an email
    if (username.includes('@') && !this.isValidEmail(username)) {
      errors.push('Username appears to be an email but is not valid');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  static isValidPassword(password: string): ValidationResult {
    const errors: string[] = [];

    if (!password || typeof password !== 'string') {
      errors.push('Password is required');
      return { isValid: false, errors };
    }

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      errors.push('Password must be 128 characters or less');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate complete Confluence credential
   */
  static validateCredential(credential: Partial<ConfluenceCredential>): ValidationResult {
    const errors: string[] = [];

    // Validate required fields
    const nameValidation = this.isValidCredentialName(credential.name || '');
    if (!nameValidation.isValid) {
      errors.push(...nameValidation.errors);
    }

    const urlValidation = this.isValidConfluenceUrl(credential.baseUrl || '');
    if (!urlValidation.isValid) {
      errors.push(...urlValidation.errors);
    }

    const spaceValidation = this.isValidSpaceKey(credential.spaceKey || '');
    if (!spaceValidation.isValid) {
      errors.push(...spaceValidation.errors);
    }

    // Validate authentication method
    if (credential.puppeteerLogin) {
      // For browser login, username and password are required
      if (credential.username) {
        const usernameValidation = this.isValidUsername(credential.username);
        if (!usernameValidation.isValid) {
          errors.push(...usernameValidation.errors);
        }
      }

      if (credential.password) {
        const passwordValidation = this.isValidPassword(credential.password);
        if (!passwordValidation.isValid) {
          errors.push(...passwordValidation.errors);
        }
      }
    } else {
      // For API token authentication, token is required
      if (credential.token) {
        const tokenValidation = this.isValidApiToken(credential.token);
        if (!tokenValidation.isValid) {
          errors.push(...tokenValidation.errors);
        }
      } else {
        errors.push('API token is required when not using browser login');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate array of credentials
   */
  static validateCredentials(credentials: any[]): ValidationResult {
    const errors: string[] = [];

    if (!Array.isArray(credentials)) {
      errors.push(messages.errors.invalidCredentialFormat);
      return { isValid: false, errors };
    }

    credentials.forEach((cred, index) => {
      const validation = this.validateCredential(cred);
      if (!validation.isValid) {
        errors.push(`Credential ${index + 1}: ${validation.errors.join(', ')}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize credential name for safe file operations
   */
  static sanitizeCredentialName(name: string): string {
    return name
      .replace(/[^a-zA-Z0-9\-_\s]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .toLowerCase()
      .substring(0, 50); // Limit length
  }

  /**
   * Check if two credentials are duplicates (same baseUrl)
   */
  static isDuplicateCredential(cred1: ConfluenceCredential, cred2: ConfluenceCredential): boolean {
    return cred1.baseUrl.toLowerCase() === cred2.baseUrl.toLowerCase();
  }
}