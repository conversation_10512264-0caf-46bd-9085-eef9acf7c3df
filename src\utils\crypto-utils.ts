/**
 * Shared cryptographic utilities extracted from SecureCredentialsManager and SecureCookieManager
 */

import * as crypto from 'crypto';

export interface EncryptedMasterKey {
  salt: string;
  iv: string;
  encryptedKey: string;
  authTag: string;
}

export interface EncryptedData {
  salt: string;
  iv: string;
  encryptedData: string;
}

export class CryptoUtils {
  // Consistent PBKDF2 parameters
  private static readonly MASTER_KEY_ITERATIONS = 100000;
  private static readonly DATA_ITERATIONS = 10000;
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly IV_LENGTH = 16; // 128 bits
  private static readonly SALT_LENGTH = 32; // 256 bits for master key, 16 for data
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly HASH_ALGORITHM = 'sha256';

  /**
   * Encrypt the master key with a user password using PBKDF2
   */
  static encryptMasterKey(masterKey: Buffer, password: string): EncryptedMasterKey {
    const salt = crypto.randomBytes(this.SALT_LENGTH);
    const iv = crypto.randomBytes(this.IV_LENGTH);
    
    // Derive key from password using PBKDF2
    const derivedKey = crypto.pbkdf2Sync(password, salt, this.MASTER_KEY_ITERATIONS, this.KEY_LENGTH, this.HASH_ALGORITHM);
    
    // Encrypt the master key
    const cipher = crypto.createCipheriv(this.ALGORITHM, derivedKey, iv);
    let encryptedKey = cipher.update(masterKey).toString('hex');
    encryptedKey += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      salt: salt.toString('hex'),
      iv: iv.toString('hex'),
      encryptedKey,
      authTag: authTag.toString('hex')
    };
  }

  /**
   * Decrypt the master key using a user password
   */
  static decryptMasterKey(encryptedMasterKey: EncryptedMasterKey, password: string): Buffer {
    const salt = Buffer.from(encryptedMasterKey.salt, 'hex');
    const iv = Buffer.from(encryptedMasterKey.iv, 'hex');
    const authTag = Buffer.from(encryptedMasterKey.authTag, 'hex');
    
    // Derive the same key using PBKDF2
    const derivedKey = crypto.pbkdf2Sync(password, salt, this.MASTER_KEY_ITERATIONS, this.KEY_LENGTH, this.HASH_ALGORITHM);
    
    // Decrypt the master key
    const decipher = crypto.createDecipheriv(this.ALGORITHM, derivedKey, iv);
    decipher.setAuthTag(authTag);
    
    let decryptedKey = decipher.update(encryptedMasterKey.encryptedKey, 'hex');
    decryptedKey = Buffer.concat([decryptedKey, decipher.final()]);
    
    return decryptedKey;
  }

  /**
   * Encrypt data using a master key
   */
  static encryptData(data: string, masterKey: Buffer): EncryptedData {
    // Generate random IV and salt for each encryption
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const salt = crypto.randomBytes(16); // Smaller salt for data encryption
    
    // Derive key using PBKDF2
    const derivedKey = crypto.pbkdf2Sync(masterKey, salt, this.DATA_ITERATIONS, this.KEY_LENGTH, this.HASH_ALGORITHM);
    
    // Encrypt the data
    const cipher = crypto.createCipheriv(this.ALGORITHM, derivedKey, iv);
    let encryptedData = cipher.update(data, 'utf8', 'hex');
    encryptedData += cipher.final('hex');
    
    // Get the auth tag and append it to encrypted data
    const authTag = cipher.getAuthTag().toString('hex');
    
    return {
      salt: salt.toString('hex'),
      iv: iv.toString('hex'),
      encryptedData: encryptedData + authTag // Store auth tag with the encrypted data
    };
  }

  /**
   * Decrypt data using a master key
   */
  static decryptData(encrypted: EncryptedData, masterKey: Buffer): string {
    // Convert hex strings back to buffers
    const salt = Buffer.from(encrypted.salt, 'hex');
    const iv = Buffer.from(encrypted.iv, 'hex');
    
    // The encrypted data includes the auth tag (last 32 hex chars = 16 bytes)
    const encryptedData = encrypted.encryptedData.slice(0, -32);
    const authTag = Buffer.from(encrypted.encryptedData.slice(-32), 'hex');
    
    // Derive the same key using PBKDF2
    const derivedKey = crypto.pbkdf2Sync(masterKey, salt, this.DATA_ITERATIONS, this.KEY_LENGTH, this.HASH_ALGORITHM);
    
    // Decrypt the data
    const decipher = crypto.createDecipheriv(this.ALGORITHM, derivedKey, iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Generate a new random encryption key
   */
  static generateKey(): Buffer {
    return crypto.randomBytes(this.KEY_LENGTH);
  }

  /**
   * Securely wipe a buffer from memory
   */
  static secureWipe(buffer: Buffer): void {
    if (buffer && buffer.length > 0) {
      buffer.fill(0);
    }
  }

  /**
   * Securely wipe multiple buffers from memory
   */
  static secureWipeMultiple(...buffers: (Buffer | undefined)[]): void {
    buffers.forEach(buffer => {
      if (buffer) {
        this.secureWipe(buffer);
      }
    });
  }

  /**
   * Create a secure buffer that automatically wipes itself
   */
  static createSecureBuffer(size: number): Buffer & { secureDispose(): void } {
    const buffer = crypto.randomBytes(size) as Buffer & { secureDispose(): void };
    
    buffer.secureDispose = () => {
      this.secureWipe(buffer);
    };
    
    return buffer;
  }

  /**
   * Perform cryptographic operation with automatic cleanup
   */
  static async withSecureCleanup<T>(
    operation: (cleanup: (buffer: Buffer) => void) => Promise<T>
  ): Promise<T> {
    const buffersToClean: Buffer[] = [];
    
    const cleanup = (buffer: Buffer) => {
      buffersToClean.push(buffer);
    };
    
    try {
      return await operation(cleanup);
    } finally {
      // Securely wipe all tracked buffers
      buffersToClean.forEach(buffer => this.secureWipe(buffer));
    }
  }

  /**
   * Check if an object has the structure of an encrypted master key
   */
  static isEncryptedMasterKey(obj: any): obj is EncryptedMasterKey {
    return !!(obj && 
           typeof obj.salt === 'string' && 
           typeof obj.iv === 'string' && 
           typeof obj.encryptedKey === 'string' && 
           typeof obj.authTag === 'string');
  }

  /**
   * Check if an object has the structure of encrypted data
   */
  static isEncryptedData(obj: any): obj is EncryptedData {
    return !!(obj && 
           typeof obj.salt === 'string' && 
           typeof obj.iv === 'string' && 
           typeof obj.encryptedData === 'string');
  }
}