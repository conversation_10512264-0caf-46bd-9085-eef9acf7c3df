import { AppError, ApplicationError } from '../errors';

/**
 * Options for error handling
 */
export interface ErrorHandlingOptions {
  /**
   * Context description for better error messages
   */
  context: string;
  
  /**
   * Whether to exit the process on error
   * @default true
   */
  exitOnError?: boolean;
  
  /**
   * Custom error handler function
   * @param error The caught error
   * @returns void or a value to return from the wrapper
   */
  onError?: (error: Error) => any;
  
  /**
   * Logger instance to use for error logging
   * @default console
   */
  logger?: {
    error: (message: string, ...args: any[]) => void;
    debug?: (message: string, ...args: any[]) => void;
  };
  silent?: boolean; // Neue Option
}

/**
 * Wraps an async function with standardized error handling
 * 
 * @param operation The async operation to execute
 * @param options Error handling options
 * @returns The result of the operation or the result of the error handler
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlingOptions
): Promise<T> {
  const {
    context,
    exitOnError = true,
    onError,
    logger = console,
    silent = false
  } = options;
  
  try {
    return await operation();
  } catch (error) {
    // Log detailed error information for debugging
    if (!silent && logger.debug) {
      logger.debug(`Error in ${context}:`, error);
    }
    
    // Handle AppError instances
    if (error instanceof AppError) {
      if (!silent) {
        logger.error(error.userMessage);
      }
      
      if (exitOnError) {
        process.exit(error.exitCode);
      }
      
      if (onError) {
        return onError(error);
      }
      
      throw error;
    }
    
    // Handle generic errors
    const appError = ApplicationError.unexpected(error instanceof Error ? error : new Error(String(error)));
    if (!silent) {
      logger.error(`${appError.userMessage} (${context})`);
    }
    
    if (exitOnError) {
      process.exit(appError.exitCode);
    }
    
    if (onError) {
      return onError(appError);
    }
    
    throw appError;
  }
}

/**
 * Synchronous version of withErrorHandling
 */
export function withErrorHandlingSync<T>(
  operation: () => T,
  options: ErrorHandlingOptions
): T {
  const {
    context,
    exitOnError = true,
    onError,
    logger = console
  } = options;
  
  try {
    return operation();
  } catch (error) {
    // Log detailed error information for debugging
    if (logger.debug) {
      logger.debug(`Error in ${context}:`, error);
    }
    
    // Handle AppError instances
    if (error instanceof AppError) {
      logger.error(error.userMessage);
      
      if (exitOnError) {
        process.exit(error.exitCode);
      }
      
      if (onError) {
        return onError(error);
      }
      
      throw error; // Re-throw if no custom handler and not exiting
    }
    
    // Handle generic errors by wrapping them in ApplicationError
    const appError = ApplicationError.unexpected(error instanceof Error ? error : new Error(String(error)));
    logger.error(`${appError.userMessage} (${context})`);
    
    if (exitOnError) {
      process.exit(appError.exitCode);
    }
    
    if (onError) {
      return onError(appError);
    }
    
    throw appError; // Re-throw if no custom handler and not exiting
  }
}
