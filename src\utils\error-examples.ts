/**
 * Examples of using the error handling system
 * This file is for demonstration purposes only
 */

import { withErrorHandling, withErrorHandlingSync } from './error-handler';
import { 
  AuthenticationError, 
  FileOperationError,
  NetworkError,
  ValidationError,
  CryptoError,
  ApplicationError
} from '../errors';

/**
 * Example of handling authentication errors
 */
export async function authenticateUser(username: string, password: string): Promise<void> {
  return withErrorHandling(async () => {
    // Example authentication logic
    if (!username || !password) {
      throw ValidationError.missingRequired(username ? 'password' : 'username');
    }
    
    if (password === 'wrong') {
      throw AuthenticationError.invalidPassword();
    }
    
    // Successful authentication would continue here
    console.log('Authentication successful');
  }, {
    context: 'user authentication',
    exitOnError: false, // Don't exit on error in this example
    logger: console
  });
}

/**
 * Example of handling file operations
 */
export async function readConfigFile(filePath: string): Promise<any> {
  return withErrorHandling(async () => {
    // Example file reading logic
    if (!filePath) {
      throw ValidationError.missingRequired('filePath');
    }
    
    // Simulate file not found
    if (filePath === 'nonexistent.json') {
      throw FileOperationError.notFound(filePath);
    }
    
    // Simulate permission error
    if (filePath === 'protected.json') {
      throw FileOperationError.permissionDenied(filePath);
    }
    
    // Successful file read would continue here
    return { config: 'example' };
  }, {
    context: 'config file reading',
    exitOnError: false
  });
}

/**
 * Example of handling network errors
 */
export async function fetchApiData(url: string): Promise<any> {
  return withErrorHandling(async () => {
    // Example API call logic
    if (!url) {
      throw ValidationError.missingRequired('url');
    }
    
    // Simulate connection error
    if (url.includes('unreachable')) {
      throw NetworkError.connectionFailed(url);
    }
    
    // Simulate HTTP error
    if (url.includes('error')) {
      const statusCode = url.includes('404') ? 404 : 500;
      throw NetworkError.httpError(url, statusCode);
    }
    
    // Successful API call would continue here
    return { data: 'example' };
  }, {
    context: 'API data fetching',
    exitOnError: false
  });
}

/**
 * Example of handling crypto errors
 */
export function decryptData(encryptedData: string, key: string): any {
  return withErrorHandlingSync(() => {
    // Example decryption logic
    if (!encryptedData) {
      throw ValidationError.missingRequired('encryptedData');
    }
    
    if (!key) {
      throw ValidationError.missingRequired('key');
    }
    
    // Simulate decryption error
    if (key === 'wrong') {
      throw CryptoError.decryptionFailed();
    }
    
    // Successful decryption would continue here
    return { decrypted: 'example' };
  }, {
    context: 'data decryption',
    exitOnError: false
  });
}