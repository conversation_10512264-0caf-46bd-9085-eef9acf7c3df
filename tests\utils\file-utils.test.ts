/**
 * Comprehensive tests for the file utilities module
 */

import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs/promises';
import { FileUtils, FileOperationOptions } from '../../src/utils/file-utils';
import { FileOperationError } from '../../src/errors/file-operation-error';
// Test utilities
const testUtils = {
  createTestData: (size: number = 100): string => {
    return 'x'.repeat(size);
  },
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
};

// Create a temporary test directory
const TEST_DIR = path.join(os.tmpdir(), `file-utils-test-${Date.now()}`);
const TEST_FILE = path.join(TEST_DIR, 'test-file.txt');
const TEST_JSON_FILE = path.join(TEST_DIR, 'test-file.json');
const TEST_SUBDIR = path.join(TEST_DIR, 'subdir');
const TEST_NESTED_FILE = path.join(TEST_SUBDIR, 'nested-file.txt');
const TEST_BINARY_FILE = path.join(TEST_DIR, 'binary-file.bin');
const TEST_LARGE_FILE = path.join(TEST_DIR, 'large-file.txt');

// Setup and teardown
beforeAll(async () => {
  // Create test directory
  await fs.mkdir(TEST_DIR, { recursive: true });
});

afterAll(async () => {
  // Clean up test directory
  try {
    await fs.rm(TEST_DIR, { recursive: true, force: true });
  } catch (error) {
    console.error('Failed to clean up test directory:', error);
  }
});

// Helper function to create test files
const createTestFile = async (filePath: string, content: string | Buffer) => {
  await fs.writeFile(filePath, content);
};

describe('FileUtils', () => {
  describe('Directory Operations', () => {
    describe('ensureDirectory', () => {
      it('should create a directory if it does not exist', async () => {
        const newDir = path.join(TEST_DIR, 'new-directory');
        await FileUtils.ensureDirectory(newDir);
        const exists = await FileUtils.fileExists(newDir);
        expect(exists).toBe(true);

        const isDir = await FileUtils.isDirectory(newDir);
        expect(isDir).toBe(true);
      });

      it('should not throw if directory already exists', async () => {
        await expect(FileUtils.ensureDirectory(TEST_DIR)).resolves.not.toThrow();
      });

      it('should create nested directories', async () => {
        const nestedDir = path.join(TEST_DIR, 'level1', 'level2', 'level3');
        await FileUtils.ensureDirectory(nestedDir);
        const exists = await FileUtils.fileExists(nestedDir);
        expect(exists).toBe(true);
      });

      it('should handle custom permissions', async () => {
        const customDir = path.join(TEST_DIR, 'custom-permissions');
        await FileUtils.ensureDirectory(customDir, 0o755);

        if (process.platform !== 'win32') {
          const stats = await fs.stat(customDir);
          const mode = stats.mode & 0o777;
          expect(mode).toBe(0o755);
        }
      });
    });

    describe('isDirectory', () => {
      it('should return true for directories', async () => {
        const isDir = await FileUtils.isDirectory(TEST_DIR);
        expect(isDir).toBe(true);
      });

      it('should return false for files', async () => {
        await createTestFile(TEST_FILE, 'test content');
        const isDir = await FileUtils.isDirectory(TEST_FILE);
        expect(isDir).toBe(false);
      });

      it('should return false for non-existent paths', async () => {
        const isDir = await FileUtils.isDirectory(path.join(TEST_DIR, 'nonexistent'));
        expect(isDir).toBe(false);
      });
    });
  });

  describe('File Existence and Stats', () => {
    describe('fileExists', () => {
      it('should return true for existing files', async () => {
        await createTestFile(TEST_FILE, 'test content');
        const exists = await FileUtils.fileExists(TEST_FILE);
        expect(exists).toBe(true);
      });

      it('should return true for existing directories', async () => {
        const exists = await FileUtils.fileExists(TEST_DIR);
        expect(exists).toBe(true);
      });

      it('should return false for non-existing files', async () => {
        const exists = await FileUtils.fileExists(path.join(TEST_DIR, 'nonexistent.txt'));
        expect(exists).toBe(false);
      });
    });

    describe('getFileStats', () => {
      it('should return stats for existing files', async () => {
        await createTestFile(TEST_FILE, 'test content');
        const stats = await FileUtils.getFileStats(TEST_FILE);
        expect(stats).not.toBeNull();
        expect(stats?.isFile()).toBe(true);
        expect(stats?.size).toBeGreaterThan(0);
      });

      it('should return stats for directories', async () => {
        const stats = await FileUtils.getFileStats(TEST_DIR);
        expect(stats).not.toBeNull();
        expect(stats?.isDirectory()).toBe(true);
      });

      it('should return null for non-existent files', async () => {
        const stats = await FileUtils.getFileStats(path.join(TEST_DIR, 'nonexistent.txt'));
        expect(stats).toBeNull();
      });
    });
  });

  describe('Basic File Operations', () => {
    describe('readFile and writeFile', () => {
      it('should write and read a file correctly', async () => {
        const content = 'Hello, world!';
        await FileUtils.writeFile(TEST_FILE, content);
        const readContent = await FileUtils.readFile(TEST_FILE);
        expect(readContent).toBe(content);
      });

      it('should handle unicode content', async () => {
        const unicodeContent = '🔐 Unicode test with émojis and spëcial chars 中文';
        await FileUtils.writeFile(TEST_FILE, unicodeContent);
        const readContent = await FileUtils.readFile(TEST_FILE);
        expect(readContent).toBe(unicodeContent);
      });

      it('should handle empty files', async () => {
        await FileUtils.writeFile(TEST_FILE, '');
        const readContent = await FileUtils.readFile(TEST_FILE);
        expect(readContent).toBe('');
      });

      it('should create parent directories when writing a file', async () => {
        const content = 'Nested file content';
        await FileUtils.writeFile(TEST_NESTED_FILE, content);
        const exists = await FileUtils.fileExists(TEST_NESTED_FILE);
        expect(exists).toBe(true);
        const readContent = await FileUtils.readFile(TEST_NESTED_FILE);
        expect(readContent).toBe(content);
      });

      it('should handle custom file options', async () => {
        const content = 'Custom options content';
        const options: FileOperationOptions = {
          mode: 0o644,
          encoding: 'utf-8',
          createDirectories: false
        };

        // Ensure directory exists first since createDirectories is false
        await FileUtils.ensureDirectory(path.dirname(TEST_FILE));
        await FileUtils.writeFile(TEST_FILE, content, options);
        const readContent = await FileUtils.readFile(TEST_FILE);
        expect(readContent).toBe(content);
      });

      it('should throw FileOperationError when reading a non-existent file', async () => {
        await expect(FileUtils.readFile(path.join(TEST_DIR, 'nonexistent.txt')))
          .rejects.toThrow(FileOperationError);
      });

      it('should handle different encodings', async () => {
        const content = 'Test content with encoding';
        await FileUtils.writeFile(TEST_FILE, content, { encoding: 'ascii' });
        const readContent = await FileUtils.readFile(TEST_FILE, 'ascii');
        expect(readContent).toBe(content);
      });
    });

    describe('readBinaryFile', () => {
      it('should read a file as binary buffer', async () => {
        const content = Buffer.from([0x01, 0x02, 0x03, 0x04, 0xFF, 0x00]);
        await createTestFile(TEST_BINARY_FILE, content);
        const readContent = await FileUtils.readBinaryFile(TEST_BINARY_FILE);
        expect(readContent).toEqual(content);
      });

      it('should handle empty binary files', async () => {
        const content = Buffer.alloc(0);
        await createTestFile(TEST_BINARY_FILE, content);
        const readContent = await FileUtils.readBinaryFile(TEST_BINARY_FILE);
        expect(readContent).toEqual(content);
        expect(readContent.length).toBe(0);
      });

      it('should throw FileOperationError for non-existent binary files', async () => {
        await expect(FileUtils.readBinaryFile(path.join(TEST_DIR, 'nonexistent.bin')))
          .rejects.toThrow(FileOperationError);
      });
    });

    describe('deleteFile', () => {
      it('should delete an existing file', async () => {
        const deleteFilePath = path.join(TEST_DIR, 'to-delete.txt');
        await createTestFile(deleteFilePath, 'delete me');

        const result = await FileUtils.deleteFile(deleteFilePath);
        expect(result).toBe(true);

        const exists = await FileUtils.fileExists(deleteFilePath);
        expect(exists).toBe(false);
      });

      it('should return false when deleting a non-existent file', async () => {
        const result = await FileUtils.deleteFile(path.join(TEST_DIR, 'nonexistent.txt'));
        expect(result).toBe(false);
      });
    });
  });

  describe('JSON Operations', () => {
    describe('readJsonFile and writeJsonFile', () => {
      it('should write and read JSON files correctly', async () => {
        const data = {
          name: 'Test',
          value: 42,
          nested: { key: 'value' },
          array: [1, 2, 3],
          boolean: true,
          null: null
        };
        await FileUtils.writeJsonFile(TEST_JSON_FILE, data);
        const readData = await FileUtils.readJsonFile(TEST_JSON_FILE);
        expect(readData).toEqual(data);
      });

      it('should handle complex nested JSON structures', async () => {
        const complexData = {
          users: [
            { id: 1, name: 'John', settings: { theme: 'dark', notifications: true } },
            { id: 2, name: 'Jane', settings: { theme: 'light', notifications: false } }
          ],
          metadata: {
            version: '1.0.0',
            created: new Date().toISOString(),
            features: ['auth', 'notifications', 'themes']
          }
        };

        await FileUtils.writeJsonFile(TEST_JSON_FILE, complexData);
        const readData = await FileUtils.readJsonFile(TEST_JSON_FILE);
        expect(readData).toEqual(complexData);
      });

      it('should handle custom indentation', async () => {
        const data = { key: 'value', nested: { inner: 'data' } };
        await FileUtils.writeJsonFile(TEST_JSON_FILE, data, { indent: 4 });

        const fileContent = await FileUtils.readFile(TEST_JSON_FILE);
        expect(fileContent).toContain('    "key"'); // 4 spaces indentation
      });

      it('should throw FileOperationError when reading invalid JSON', async () => {
        await createTestFile(TEST_JSON_FILE, 'not valid json');
        await expect(FileUtils.readJsonFile(TEST_JSON_FILE))
          .rejects.toThrow(FileOperationError);
      });
    });
  });

  describe('Additional File Operations', () => {
    describe('readBinaryFile', () => {
      it('should read binary file successfully', async () => {
        const testData = Buffer.from([0x89, 0x50, 0x4E, 0x47]); // PNG header
        await fs.writeFile(TEST_FILE, testData);

        const result = await FileUtils.readBinaryFile(TEST_FILE);

        expect(result).toEqual(testData);
      });

      it('should handle file not found for binary file', async () => {
        await expect(FileUtils.readBinaryFile('missing.png')).rejects.toThrow(FileOperationError);
      });
    });

    describe('writeFile', () => {
      it('should write file successfully', async () => {
        await FileUtils.writeFile(TEST_FILE, 'test content');

        const content = await fs.readFile(TEST_FILE, 'utf8');
        expect(content).toBe('test content');
      });

      it('should write file with custom encoding', async () => {
        await FileUtils.writeFile(TEST_FILE, 'test content', { encoding: 'ascii' });

        const content = await fs.readFile(TEST_FILE, 'ascii');
        expect(content).toBe('test content');
      });

      it('should write binary data', async () => {
        const testData = Buffer.from([0x89, 0x50, 0x4E, 0x47]);

        await FileUtils.writeFile(TEST_FILE, testData);

        const result = await fs.readFile(TEST_FILE);
        expect(result).toEqual(testData);
      });

      it('should create directories when needed', async () => {
        const nestedFile = path.join(TEST_DIR, 'nested', 'deep', 'file.txt');

        await FileUtils.writeFile(nestedFile, 'content', { createDirectories: true });

        const content = await fs.readFile(nestedFile, 'utf8');
        expect(content).toBe('content');
      });
    });

    describe('ensureDirectory', () => {
      it('should create directory if it does not exist', async () => {
        const newDir = path.join(TEST_DIR, 'new-directory');

        await FileUtils.ensureDirectory(newDir);

        const exists = await FileUtils.fileExists(newDir);
        expect(exists).toBe(true);
      });

      it('should not fail if directory already exists', async () => {
        await FileUtils.ensureDirectory(TEST_DIR);

        const exists = await FileUtils.fileExists(TEST_DIR);
        expect(exists).toBe(true);
      });

      it('should create directory with custom mode', async () => {
        const newDir = path.join(TEST_DIR, 'custom-mode-dir');

        await FileUtils.ensureDirectory(newDir, 0o755);

        const exists = await FileUtils.fileExists(newDir);
        expect(exists).toBe(true);
      });
    });

    describe('deleteFile', () => {
      it('should delete file successfully', async () => {
        await createTestFile(TEST_FILE, 'test content');

        await FileUtils.deleteFile(TEST_FILE);

        const exists = await FileUtils.fileExists(TEST_FILE);
        expect(exists).toBe(false);
      });

      it('should handle file not found during deletion', async () => {
        const result = await FileUtils.deleteFile('missing.txt');
        expect(result).toBe(false);
      });
    });

    describe('getFileStats', () => {
      it('should get file stats successfully', async () => {
        await createTestFile(TEST_FILE, 'test content');

        const stats = await FileUtils.getFileStats(TEST_FILE);

        expect(stats).not.toBeNull();
        expect(stats!.size).toBeGreaterThan(0);
        expect(stats!.isFile()).toBe(true);
        expect(stats!.isDirectory()).toBe(false);
      });

      it('should return null for non-existent files', async () => {
        const stats = await FileUtils.getFileStats('missing.txt');
        expect(stats).toBeNull();
      });
    });

    describe('isDirectory', () => {
      it('should correctly identify directories', async () => {
        const isDir = await FileUtils.isDirectory(TEST_DIR);
        expect(isDir).toBe(true);
      });

      it('should return false for files', async () => {
        await createTestFile(TEST_FILE, 'test content');
        const isDir = await FileUtils.isDirectory(TEST_FILE);
        expect(isDir).toBe(false);
      });

      it('should return false for non-existent paths', async () => {
        const nonExistent = path.join(TEST_DIR, 'does-not-exist');
        const isDir = await FileUtils.isDirectory(nonExistent);
        expect(isDir).toBe(false);
      });
    });

    describe('copyFile', () => {
      it('should copy file successfully', async () => {
        await createTestFile(TEST_FILE, 'test content');
        const destFile = path.join(TEST_DIR, 'copied.txt');

        await FileUtils.copyFile(TEST_FILE, destFile);

        const content = await FileUtils.readFile(destFile);
        expect(content).toBe('test content');
      });

      it('should handle source file not found', async () => {
        await expect(FileUtils.copyFile('missing.txt', 'dest.txt')).rejects.toThrow(FileOperationError);
      });

      it('should copy with overwrite option', async () => {
        await createTestFile(TEST_FILE, 'original content');
        const destFile = path.join(TEST_DIR, 'dest.txt');
        await createTestFile(destFile, 'existing content');

        await FileUtils.copyFile(TEST_FILE, destFile, { overwrite: true });

        const content = await FileUtils.readFile(destFile);
        expect(content).toBe('original content');
      });
    });

    describe('backupFile', () => {
      it('should create backup successfully', async () => {
        await createTestFile(TEST_FILE, 'backup content');

        const backupPath = await FileUtils.backupFile(TEST_FILE);

        expect(backupPath).not.toBeNull();
        expect(backupPath).toContain('.backup.');
        const backupContent = await FileUtils.readFile(backupPath!);
        expect(backupContent).toBe('backup content');
      });

      it('should return null for non-existent files', async () => {
        const backupPath = await FileUtils.backupFile('missing.txt');
        expect(backupPath).toBeNull();
      });
    });

    describe('secureRead and secureWrite', () => {
      it('should securely read and write files', async () => {
        const content = 'secure content';

        await FileUtils.secureWrite(TEST_FILE, content);
        const readContent = await FileUtils.secureRead(TEST_FILE);

        expect(readContent).toBe(content);
      });

      it('should use secure permissions by default', async () => {
        await FileUtils.secureWrite(TEST_FILE, 'secure content');

        const stats = await FileUtils.getFileStats(TEST_FILE);
        expect(stats).not.toBeNull();
        // Check that file has restrictive permissions (owner only)
        // On Windows, permissions might be different, so just check it's not world-readable
        const permissions = stats!.mode & 0o777;
        expect(permissions).toBeLessThanOrEqual(0o700); // At most owner permissions
      });
    });
  });

  describe('Advanced Features', () => {
    describe('getSecurePath', () => {
      it('should create secure path in home directory', () => {
        const securePath = FileUtils.getSecurePath('test', 'file.txt');
        expect(securePath).toContain('test');
        expect(securePath).toContain('file.txt');
      });

      it('should handle multiple path segments', () => {
        const securePath = FileUtils.getSecurePath('app', 'data', 'config.json');
        expect(securePath).toContain('app');
        expect(securePath).toContain('data');
        expect(securePath).toContain('config.json');
      });
    });

    describe('readFileStream', () => {
      it('should read file using streaming', async () => {
        const content = 'streaming content test';
        await createTestFile(TEST_FILE, content);

        const streamedContent = await FileUtils.readFileStream(TEST_FILE);

        expect(streamedContent).toBe(content);
      });

      it('should handle custom chunk size', async () => {
        const content = 'a'.repeat(1000);
        await createTestFile(TEST_FILE, content);

        const streamedContent = await FileUtils.readFileStream(TEST_FILE, 100);

        expect(streamedContent).toBe(content);
      });
    });

    describe('writeFileStream', () => {
      it('should write file using streaming', async () => {
        const content = 'streaming write test';

        await FileUtils.writeFileStream(TEST_FILE, content);

        const readContent = await FileUtils.readFile(TEST_FILE);
        expect(readContent).toBe(content);
      });

      it('should write binary data using streaming', async () => {
        const binaryData = Buffer.from([0x89, 0x50, 0x4E, 0x47]);

        await FileUtils.writeFileStream(TEST_FILE, binaryData);

        const readData = await FileUtils.readBinaryFile(TEST_FILE);
        expect(readData).toEqual(binaryData);
      });
    });

    describe('batchFileOperations', () => {
      it('should execute batch operations successfully', async () => {
        const operations = [
          () => FileUtils.writeFile(path.join(TEST_DIR, 'batch1.txt'), 'content1'),
          () => FileUtils.writeFile(path.join(TEST_DIR, 'batch2.txt'), 'content2'),
          () => FileUtils.writeFile(path.join(TEST_DIR, 'batch3.txt'), 'content3')
        ];

        await FileUtils.batchFileOperations(operations, 2);

        const content1 = await FileUtils.readFile(path.join(TEST_DIR, 'batch1.txt'));
        const content2 = await FileUtils.readFile(path.join(TEST_DIR, 'batch2.txt'));
        const content3 = await FileUtils.readFile(path.join(TEST_DIR, 'batch3.txt'));

        expect(content1).toBe('content1');
        expect(content2).toBe('content2');
        expect(content3).toBe('content3');
      });

      it('should handle batch operation failures', async () => {
        const operations = [
          () => FileUtils.writeFile(path.join(TEST_DIR, 'good.txt'), 'content'),
          async () => { await FileUtils.readFile('non-existent-file.txt'); }
        ];

        await expect(FileUtils.batchFileOperations(operations)).rejects.toThrow();
      });
    });

    describe('withTimeout', () => {
      it('should complete operation within timeout', async () => {
        const operation = () => FileUtils.writeFile(TEST_FILE, 'timeout test');

        await FileUtils.withTimeout(operation, 5000, 'write test');

        const content = await FileUtils.readFile(TEST_FILE);
        expect(content).toBe('timeout test');
      });

      it('should timeout for slow operations', async () => {
        const slowOperation = () => new Promise<void>(resolve => setTimeout(resolve, 1000));

        await expect(
          FileUtils.withTimeout(slowOperation, 100, 'slow operation')
        ).rejects.toThrow();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty file path', async () => {
      await expect(FileUtils.fileExists('')).resolves.toBe(false);
    });

    it('should handle special characters in file paths', async () => {
      const specialPath = path.join(TEST_DIR, 'test with spaces & symbols.txt');
      await createTestFile(specialPath, 'content');

      const exists = await FileUtils.fileExists(specialPath);
      expect(exists).toBe(true);
    });

    it('should handle unicode file names', async () => {
      const unicodePath = path.join(TEST_DIR, 'тест-файл-🚀.txt');
      await createTestFile(unicodePath, 'unicode content');

      const content = await FileUtils.readFile(unicodePath);
      expect(content).toBe('unicode content');
    });

    it('should handle very large files efficiently', async () => {
      const largeContent = 'x'.repeat(100000); // 100KB
      await createTestFile(TEST_FILE, largeContent);

      const readContent = await FileUtils.readFile(TEST_FILE);
      expect(readContent.length).toBe(largeContent.length);
    });

    it('should handle concurrent file operations', async () => {
      const operations = Array.from({ length: 10 }, (_, i) =>
        FileUtils.writeFile(path.join(TEST_DIR, `concurrent-${i}.txt`), `content-${i}`)
      );

      await Promise.all(operations);

      // Verify all files were created
      for (let i = 0; i < 10; i++) {
        const content = await FileUtils.readFile(path.join(TEST_DIR, `concurrent-${i}.txt`));
        expect(content).toBe(`content-${i}`);
      }
    });
  });
});