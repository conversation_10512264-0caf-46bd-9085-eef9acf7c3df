/**
 * Error class for cryptographic operations
 */

import { AppError } from './app-error';

export class CryptoError extends AppError {
  readonly code = 'CRYPTO_ERROR';
  readonly userMessage: string;
  readonly exitCode = 3;
  
  constructor(message: string, userMessage: string, details?: Record<string, any>) {
    super(message, details);
    this.userMessage = userMessage;
  }
  
  static keyNotInitialized(details?: Record<string, any>): CryptoError {
    return new CryptoError(
      'Encryption key not initialized',
      'The encryption key has not been initialized. Please provide a password.',
      details
    );
  }
  
  static invalidPassword(details?: Record<string, any>): CryptoError {
    return new CryptoError(
      'Invalid password or corrupted key file',
      'The provided password is incorrect or the key file is corrupted.',
      details
    );
  }
  
  static decryptionFailed(details?: Record<string, any>): CryptoError {
    return new CryptoError(
      'Failed to decrypt data',
      'Failed to decrypt the data. The data may be corrupted or the encryption key is invalid.',
      details
    );
  }
  
  static encryptionFailed(details?: Record<string, any>): CryptoError {
    return new CryptoError(
      'Failed to encrypt data',
      'Failed to encrypt the data.',
      details
    );
  }
}