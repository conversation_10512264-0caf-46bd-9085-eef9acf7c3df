import { AppError } from './app-error';

/**
 * Error thrown when network operations fail
 */
export class NetworkError extends AppError {
  readonly code = 'NETWORK_ERROR';
  readonly exitCode = 2;
  
  constructor(
    message: string,
    public readonly userMessage: string = 'A network operation failed. Please check your internet connection.',
    public readonly url?: string,
    public readonly statusCode?: number,
    details?: Record<string, any>
  ) {
    super(message, { ...details, url, statusCode });
  }
  
  /**
   * Factory method for connection errors
   */
  static connectionFailed(url: string, originalError?: Error): NetworkError {
    return new NetworkError(
      `Failed to connect to ${url}`,
      `Failed to connect to server. Please check your internet connection.`,
      url,
      undefined,
      { originalError: originalError?.message }
    );
  }
  
  /**
   * Factory method for timeout errors
   */
  static timeout(url: string, timeoutMs: number): NetworkError {
    return new NetworkError(
      `Request to ${url} timed out after ${timeoutMs}ms`,
      `The server took too long to respond. Please try again later.`,
      url,
      undefined,
      { timeoutMs }
    );
  }
  
  /**
   * Factory method for HTTP errors
   */
  static httpError(url: string, statusCode: number, responseData?: any): NetworkError {
    let userMessage = 'An error occurred while communicating with the server.';
    
    // Provide more specific messages for common status codes
    if (statusCode === 401 || statusCode === 403) {
      userMessage = 'Authentication failed. Please check your credentials or login again.';
    } else if (statusCode === 404) {
      userMessage = 'The requested resource was not found on the server.';
    } else if (statusCode === 500) {
      userMessage = 'The server encountered an error. Please try again later.';
    } else if (statusCode === 503) {
      userMessage = 'The server is temporarily unavailable. Please try again later.';
    }
    
    return new NetworkError(
      `HTTP error ${statusCode} from ${url}`,
      userMessage,
      url,
      statusCode,
      { responseData }
    );
  }
  
  /**
   * Factory method for API errors
   */
  static apiError(url: string, statusCode: number, errorCode?: string, errorMessage?: string): NetworkError {
    return new NetworkError(
      `API error: ${errorMessage || 'Unknown error'}`,
      errorMessage || 'The server returned an error response.',
      url,
      statusCode,
      { errorCode }
    );
  }
}