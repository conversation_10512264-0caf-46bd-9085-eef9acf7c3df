/**
 * Secure credentials manager for storing and retrieving Confluence API credentials
 */

import { CryptoUtils, EncryptedMasterKey, EncryptedData } from '../utils/crypto-utils';
import { FileUtils } from '../utils/file-utils';
import { logger } from '../utils/logger';
import { defaultConfig } from '../config/app-config';
import { messages } from '../config/messages';
import { withErrorHandling } from '../utils/error-handler';
import { CryptoError, ValidationError } from '../errors';
import { PerformanceUtils, ResourceCleanup } from '../utils/performance-utils';

export interface ConfluenceCredential {
  name: string;
  baseUrl: string;
  token?: string;
  spaceKey: string;
  puppeteerLogin: boolean;
  username?: string;
  password?: string;
}

class SecureCredentialsManager implements ResourceCleanup {
  private readonly keyFilePath: string;
  private readonly encryptedFilePath: string;
  private encryptionKey?: Buffer;
  private credentials: ConfluenceCredential[] = [];
  private cleanupRegistered = false;
  
  constructor() {
    // Use centralized configuration for file paths
    this.keyFilePath = defaultConfig.paths.keyFile;
    this.encryptedFilePath = defaultConfig.paths.credentialsFile;
    
    // Register cleanup on first use
    if (!this.cleanupRegistered) {
      PerformanceUtils.registerCleanup(this);
      this.cleanupRegistered = true;
    }
  }
  
  /**
   * Check if the key file exists and is encrypted (new format)
   */
  private async isKeyFileEncrypted(): Promise<boolean> {
    return withErrorHandling(async () => {
      try {
        const keyContent = await FileUtils.readFile(this.keyFilePath);
        const parsed = JSON.parse(keyContent);
        return CryptoUtils.isEncryptedMasterKey(parsed);
      } catch {
        return false;
      }
    }, {
      context: 'checking key file encryption',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Initialize the credentials manager
   * Creates necessary directories and files if they don't exist
   */
  async initialize(password?: string): Promise<void> {
    return withErrorHandling(async () => {
      const secureConfigDir = defaultConfig.paths.configDir;
      
      // Create secure config directory if it doesn't exist
      await FileUtils.ensureDirectory(secureConfigDir);
      
      // Check if key file exists
      const keyFileExists = await FileUtils.fileExists(this.keyFilePath);
      
      if (keyFileExists) {
        // Key file exists - check if it's encrypted or plain
        const isEncrypted = await this.isKeyFileEncrypted();
        
        if (isEncrypted) {
          // New encrypted format - password required
          if (!password) {
            throw new CryptoError(
              'Password required to decrypt master key',
              messages.errors.passwordRequired
            );
          }
          
          const keyContent = await FileUtils.readFile(this.keyFilePath);
          const encryptedMasterKey: EncryptedMasterKey = JSON.parse(keyContent);
          
          try {
            this.encryptionKey = CryptoUtils.decryptMasterKey(encryptedMasterKey, password);
          } catch (error) {
            throw new CryptoError(
              'Invalid password or corrupted key file',
              messages.errors.invalidPassword,
              { originalError: error instanceof Error ? error.message : String(error) }
            );
          }
        } else {
          // Old plain format - migrate to encrypted format if password provided
          this.encryptionKey = await FileUtils.readBinaryFile(this.keyFilePath);
          
          if (password) {
            // Migrate to encrypted format
            const encryptedMasterKey = CryptoUtils.encryptMasterKey(this.encryptionKey, password);
            await FileUtils.writeJsonFile(this.keyFilePath, encryptedMasterKey, { mode: 0o600 });
            logger.info(messages.info.masterKeyMigrated);
          } else {
            logger.warn(messages.warnings.plainKeyFormat);
          }
        }
      } else {
        // No key file exists - create new one
        this.encryptionKey = CryptoUtils.generateKey();
        
        if (password) {
          // Create encrypted key file
          const encryptedMasterKey = CryptoUtils.encryptMasterKey(this.encryptionKey, password);
          await FileUtils.writeJsonFile(this.keyFilePath, encryptedMasterKey, { mode: 0o600 });
        } else {
          // Create plain key file (backward compatibility)
          await FileUtils.writeFile(this.keyFilePath, this.encryptionKey, { mode: 0o600 });
          logger.warn(messages.warnings.plainKeyFormat);
        }
      }
      
      // Try to load existing credentials
      try {
        await this.loadCredentials();
      } catch (err) {
        // No credentials file or invalid format - start with empty credentials
        this.credentials = [];
      }
      
      logger.debug('Credentials manager initialized successfully');
    }, {
      context: 'credentials manager initialization',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Import credentials from a plain JSON file
   */
  async importFromFile(filePath: string): Promise<void> {
    return withErrorHandling(async () => {
      const fileContent = await FileUtils.readFile(filePath);
      const importedCredentials = JSON.parse(fileContent);
      
      if (!Array.isArray(importedCredentials)) {
        throw new ValidationError(
          'Invalid credentials format',
          messages.errors.invalidCredentialFormat
        );
      }
      
      // Validate each credential
      for (const cred of importedCredentials) {
        if (!cred.name || !cred.baseUrl || !cred.spaceKey) {
          throw new ValidationError(
            'Missing required credential fields',
            messages.errors.missingRequiredFields
          );
        }
      }
      
      this.credentials = importedCredentials;
      await this.saveCredentials();
      
      logger.info(messages.info.credentialsImported(importedCredentials.length));
    }, {
      context: 'credentials import',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Export credentials to a plain JSON file
   */
  async exportToFile(filePath: string): Promise<void> {
    return withErrorHandling(async () => {
      if (!this.encryptionKey) {
        throw new CryptoError(
          'Encryption key not initialized',
          messages.errors.encryptionKeyNotInitialized
        );
      }
      
      // Load current credentials if not already loaded
      if (this.credentials.length === 0) {
        try {
          await this.loadCredentials();
        } catch (error) {
          // If no credentials file exists, export empty array
          logger.info('No credentials found to export.');
        }
      }
      
      // Create a clean copy of credentials for export
      const exportData = this.credentials.map(cred => ({
        name: cred.name,
        baseUrl: cred.baseUrl,
        token: cred.token,
        spaceKey: cred.spaceKey,
        puppeteerLogin: cred.puppeteerLogin,
        username: cred.username,
        password: cred.password
      }));
      
      // Write to file with proper formatting
      await FileUtils.writeJsonFile(filePath, exportData, { mode: 0o600 });
      
      logger.info(messages.info.credentialsExported(exportData.length, filePath));
      logger.warn(messages.warnings.sensitiveDataExported);
    }, {
      context: 'credentials export',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Get all credentials
   */
  getCredentials(): ConfluenceCredential[] {
    return [...this.credentials];
  }
  
  /**
   * Get a specific credential by base URL
   */
  getCredentialByBaseUrl(baseUrl: string): ConfluenceCredential | undefined {
    return this.credentials.find(cred => cred.baseUrl === baseUrl);
  }
  
  /**
   * Add or update a credential
   */
  async addOrUpdateCredential(credential: ConfluenceCredential): Promise<void> {
    return withErrorHandling(async () => {
      const existingIndex = this.credentials.findIndex(cred => cred.baseUrl === credential.baseUrl);
      
      if (existingIndex >= 0) {
        this.credentials[existingIndex] = credential;
      } else {
        this.credentials.push(credential);
      }
      
      await this.saveCredentials();
    }, {
      context: 'credential update',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Remove a credential by base URL
   */
  async removeCredential(baseUrl: string): Promise<boolean> {
    return withErrorHandling(async () => {
      const initialLength = this.credentials.length;
      this.credentials = this.credentials.filter(cred => cred.baseUrl !== baseUrl);
      
      if (this.credentials.length !== initialLength) {
        await this.saveCredentials();
        return true;
      }
      
      return false;
    }, {
      context: 'credential removal',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Save credentials to encrypted file
   */
  private async saveCredentials(): Promise<void> {
    return withErrorHandling(async () => {
      if (!this.encryptionKey) {
        throw new CryptoError(
          'Encryption key not initialized',
          messages.errors.encryptionKeyNotInitialized
        );
      }
      
      const credentialsJson = JSON.stringify(this.credentials);
      const encryptedData = CryptoUtils.encryptData(credentialsJson, this.encryptionKey);
      
      await FileUtils.writeJsonFile(this.encryptedFilePath, encryptedData, { mode: 0o600 });
    }, {
      context: 'credentials saving',
      exitOnError: false,
      logger
    });
  }
  
  /**
   * Load credentials from encrypted file
   */
  private async loadCredentials(): Promise<void> {
    return withErrorHandling(async () => {
      if (!this.encryptionKey) {
        throw new CryptoError(
          'Encryption key not initialized',
          messages.errors.encryptionKeyNotInitialized
        );
      }
      
      const fileExists = await FileUtils.fileExists(this.encryptedFilePath);
      if (!fileExists) {
        this.credentials = [];
        return;
      }
      
      const encryptedContent = await FileUtils.readFile(this.encryptedFilePath);
      const encryptedData: EncryptedData = JSON.parse(encryptedContent);
      
      const decrypted = CryptoUtils.decryptData(encryptedData, this.encryptionKey);
      this.credentials = JSON.parse(decrypted);
    }, {
      context: 'credentials loading',
      exitOnError: false,
      logger
    });
  }

  /**
   * Cleanup sensitive data from memory
   */
  async cleanup(): Promise<void> {
    logger.debug('Cleaning up SecureCredentialsManager...');
    
    // Clear credentials array and wipe sensitive data
    this.credentials.forEach(cred => {
      if (cred.password) {
        PerformanceUtils.secureWipe(cred.password);
      }
      if (cred.token) {
        PerformanceUtils.secureWipe(cred.token);
      }
    });
    this.credentials.length = 0;
    
    // Securely wipe encryption key
    if (this.encryptionKey) {
      CryptoUtils.secureWipe(this.encryptionKey);
      this.encryptionKey = undefined;
    }
    
    // Force garbage collection
    PerformanceUtils.forceGarbageCollection();
    
    logger.debug('SecureCredentialsManager cleanup completed');
  }
}

export { SecureCredentialsManager };
