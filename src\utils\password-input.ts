/**
 * Centralized password input utility with proper TTY/non-TTY support
 */

/**
 * Prompts for a password with asterisk masking
 * @param query The prompt message to display
 * @returns Promise that resolves to the entered password
 */
export function askPassword(query: string): Promise<string> {
  return new Promise(resolve => {
    const stdin = process.stdin;
    const stdout = process.stdout;

    stdout.write(query);

    // Check if stdin is a TTY (interactive terminal)
    if (stdin.isTTY) {
      // Store original settings
      const originalRawMode = stdin.isRaw;

      // Enable raw mode to capture individual keystrokes
      stdin.setRawMode(true);
      stdin.resume();
      stdin.setEncoding('utf8');

      let password = '';

      const cleanup = () => {
        stdin.setRawMode(originalRawMode);
        stdin.pause();
        stdin.removeListener('data', onData);
      };

      const onData = (data: Buffer | string) => {
        // Convert to string and handle as raw bytes
        const input = Buffer.isBuffer(data) ? data.toString('utf8') : data;

        // Process each character individually
        for (let i = 0; i < input.length; i++) {
          const char = input[i];
          const charCode = char.charCodeAt(0);

          // Handle special keys first
          if (charCode === 13 || charCode === 10) { // Enter
            cleanup();
            stdout.write('\n');
            resolve(password);
            return;
          } else if (charCode === 3) { // Ctrl+C
            cleanup();
            stdout.write('^C\n');
            resolve(''); // Return empty string instead of exiting
            return;
          } else if (charCode === 4) { // Ctrl+D
            cleanup();
            stdout.write('\n');
            resolve(password);
            return;
          } else if (charCode === 127 || charCode === 8) { // Backspace/Delete
            if (password.length > 0) {
              password = password.slice(0, -1);
              // Simply erase the asterisk
              stdout.write('\b \b');
            }
          } else if (charCode >= 32 && charCode <= 126) { // Printable characters
            password += char;
            // Display an asterisk for each character
            stdout.write('*');
          }
          // All other characters (including control chars) are ignored
        }
      };

      stdin.on('data', onData);

    } else {
      // Fallback for non-interactive environments (piped input, etc.)
      stdin.resume();
      stdin.setEncoding('utf8');

      let password = '';

      const onData = (data: string) => {
        password += data;
        if (password.includes('\n')) {
          password = password.replace(/\n$/, ''); // Remove trailing newline
          stdin.pause();
          stdin.removeListener('data', onData);
          stdout.write('\n');
          resolve(password);
        }
      };

      stdin.on('data', onData);
    }
  });
}

/**
 * Prompts for a password with confirmation
 * @param query The prompt message to display
 * @returns Promise that resolves to the entered password if both entries match
 */
export async function askPasswordWithConfirmation(query: string): Promise<string> {
  const password1 = await askPassword(query);
  if (password1 === '') {
    return ''; // User cancelled
  }

  const password2 = await askPassword('Confirm password: ');
  if (password2 === '') {
    return ''; // User cancelled
  }

  if (password1 !== password2) {
    throw new Error('Passwords do not match');
  }

  return password1;
}