/**
 * Tests for HTTP client service
 */

import axios, { AxiosInstance } from 'axios';

// Mock axios BEFORE importing the module
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Create mock axios instance early
const mockAxiosInstance = {
  interceptors: {
    request: {
      use: jest.fn() as any
    },
    response: {
      use: jest.fn() as any
    }
  },
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  head: jest.fn(),
  options: jest.fn(),
  request: jest.fn(),
  getUri: jest.fn(),
  defaults: {} as any
} as any;

// Set up the mock before any imports
mockedAxios.create.mockReturnValue(mockAxiosInstance);

// NOW import the modules that use axios
import { createHttpClient, createConfluenceHttpClient, httpClient } from '../../src/services/http-client';
import { defaultConfig } from '../../src/config/app-config';
import { logger } from '../../src/utils/logger';

// Mock logger
jest.mock('../../src/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn()
  }
}));

// Mock default config
jest.mock('../../src/config/app-config', () => ({
  defaultConfig: {
    http: {
      timeout: 30000,
      userAgent: 'confluence-upload/1.0.0 (Node.js; Confluence Page Updater)'
    }
  }
}));

describe('HTTP Client Service', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset the mock return value
    mockedAxios.create.mockReturnValue(mockAxiosInstance);
  });

  describe('createHttpClient', () => {
    it('should create axios instance with default configuration', () => {
      const client = createHttpClient();

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          timeout: expect.any(Number),
          headers: expect.objectContaining({
            'User-Agent': expect.any(String),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          })
        })
      );

      expect(client).toBe(mockAxiosInstance);
    });

    it('should create axios instance with custom baseURL', () => {
      const baseURL = 'https://api.example.com';
      createHttpClient(baseURL);

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL,
          timeout: expect.any(Number),
          headers: expect.objectContaining({
            'User-Agent': expect.any(String),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          })
        })
      );
    });

    it('should merge additional configuration', () => {
      const additionalConfig = {
        timeout: 60000,
        headers: {
          'Authorization': 'Bearer token123',
          'Custom-Header': 'custom-value'
        }
      };

      createHttpClient('https://api.example.com', additionalConfig);

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: 'https://api.example.com',
          timeout: 60000,
          headers: expect.objectContaining({
            'Authorization': 'Bearer token123',
            'Custom-Header': 'custom-value'
          })
        })
      );
    });

    it('should setup request interceptor', () => {
      createHttpClient();

      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalledWith(
        expect.any(Function),
        expect.any(Function)
      );
    });

    it('should setup response interceptor', () => {
      createHttpClient();

      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalledWith(
        expect.any(Function),
        expect.any(Function)
      );
    });

    describe('request interceptor', () => {
      it('should log request details', () => {
        createHttpClient();

        const requestInterceptor = (mockAxiosInstance.interceptors.request.use as jest.Mock).mock.calls[0][0];
        const mockConfig = {
          method: 'get',
          url: '/api/test',
          headers: {
            'User-Agent': 'test-agent'
          }
        };

        const result = requestInterceptor(mockConfig);

        expect(logger.debug).toHaveBeenCalledWith(
          'HTTP Request: GET /api/test',
          {
            headers: mockConfig.headers,
            userAgent: 'test-agent'
          }
        );
        expect(result).toBe(mockConfig);
      });

      it('should handle request errors', () => {
        createHttpClient();

        const requestErrorInterceptor = (mockAxiosInstance.interceptors.request.use as jest.Mock).mock.calls[0][1];
        const error = new Error('Request failed');

        expect(() => requestErrorInterceptor(error)).rejects.toThrow('Request failed');
        expect(logger.error).toHaveBeenCalledWith('HTTP Request Error:', error);
      });
    });

    describe('response interceptor', () => {
      it('should log successful response details', () => {
        createHttpClient();

        const responseInterceptor = (mockAxiosInstance.interceptors.response.use as jest.Mock).mock.calls[0][0];
        const mockResponse = {
          status: 200,
          statusText: 'OK',
          config: {
            url: '/api/test'
          },
          data: { success: true }
        };

        const result = responseInterceptor(mockResponse);

        expect(logger.debug).toHaveBeenCalledWith(
          'HTTP Response: 200 /api/test',
          {
            status: 200,
            statusText: 'OK'
          }
        );
        expect(result).toBe(mockResponse);
      });

      it('should handle response errors', () => {
        createHttpClient();

        const responseErrorInterceptor = (mockAxiosInstance.interceptors.response.use as jest.Mock).mock.calls[0][1];
        const error = {
          response: {
            status: 404,
            statusText: 'Not Found'
          },
          config: {
            url: '/api/test'
          },
          message: 'Request failed with status code 404'
        };

        expect(() => responseErrorInterceptor(error)).rejects.toEqual(error);
        expect(logger.error).toHaveBeenCalledWith('HTTP Response Error:', {
          status: 404,
          statusText: 'Not Found',
          url: '/api/test',
          message: 'Request failed with status code 404'
        });
      });

      it('should handle response errors without response object', () => {
        createHttpClient();

        const responseErrorInterceptor = (mockAxiosInstance.interceptors.response.use as jest.Mock).mock.calls[0][1];
        const error = {
          message: 'Network Error',
          config: {
            url: '/api/test'
          }
        };

        expect(() => responseErrorInterceptor(error)).rejects.toEqual(error);
        expect(logger.error).toHaveBeenCalledWith('HTTP Response Error:', {
          status: undefined,
          statusText: undefined,
          url: '/api/test',
          message: 'Network Error'
        });
      });
    });
  });

  describe('createConfluenceHttpClient', () => {
    it('should create HTTP client with Confluence-specific headers', () => {
      const baseURL = 'https://test.atlassian.net';
      createConfluenceHttpClient(baseURL);

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL,
          headers: expect.objectContaining({
            'X-Atlassian-Token': 'no-check'
          })
        })
      );
    });

    it('should merge additional headers', () => {
      const baseURL = 'https://test.atlassian.net';
      const additionalHeaders = {
        'Authorization': 'Basic dGVzdDp0ZXN0',
        'Custom-Header': 'custom-value'
      };

      createConfluenceHttpClient(baseURL, additionalHeaders);

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL,
          headers: expect.objectContaining({
            'X-Atlassian-Token': 'no-check',
            'Authorization': 'Basic dGVzdDp0ZXN0',
            'Custom-Header': 'custom-value'
          })
        })
      );
    });

    it('should allow overriding User-Agent in additional headers', () => {
      const baseURL = 'https://test.atlassian.net';
      const additionalHeaders = {
        'User-Agent': 'custom-user-agent/2.0.0'
      };

      createConfluenceHttpClient(baseURL, additionalHeaders);

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL,
          headers: expect.objectContaining({
            'User-Agent': 'custom-user-agent/2.0.0',
            'X-Atlassian-Token': 'no-check'
          })
        })
      );
    });
  });

  describe('httpClient', () => {
    it('should export a default HTTP client instance', () => {
      expect(httpClient).toBe(mockAxiosInstance);
      // The httpClient is created at module load time, so we just verify it exists
      expect(httpClient).toBeDefined();
    });
  });
});
