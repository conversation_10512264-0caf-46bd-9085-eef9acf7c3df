/**
 * File system utilities with consistent error handling and security
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import { logger } from './logger';
import { messages } from '../config/messages';
import { FileOperationError } from '../errors/file-operation-error';
import { withErrorHandling } from './error-handler';
import { PerformanceUtils } from './performance-utils';

export interface FileOperationOptions {
  mode?: number;
  encoding?: BufferEncoding;
  createDirectories?: boolean;
}

export class FileUtils {
  /**
   * Safely create directory with proper permissions
   */
  static async ensureDirectory(dirPath: string, mode: number = 0o700): Promise<void> {
    return withErrorHandling(async () => {
      try {
        await fs.access(dirPath);
        logger.debug(`Directory already exists: ${dirPath}`);
      } catch {
        try {
          await fs.mkdir(dirPath, { recursive: true, mode });
          logger.debug(`Created directory: ${dirPath}`);
        } catch (error) {
          throw FileOperationError.directoryCreationError(dirPath, error instanceof Error ? error : undefined);
        }
      }
    }, {
      context: 'directory creation',
      exitOnError: false,
      logger
    });
  }

  /**
   * Check if file exists
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Safely read file with error handling
   */
  static async readFile(filePath: string, encoding: BufferEncoding = 'utf-8'): Promise<string> {
    return withErrorHandling(async () => {
      try {
        return await fs.readFile(filePath, encoding);
      } catch (error) {
        // Check if file not found
        if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
          throw FileOperationError.notFound(filePath);
        }
        // Check if permission denied
        if ((error as NodeJS.ErrnoException).code === 'EACCES') {
          throw FileOperationError.permissionDenied(filePath);
        }
        // Generic read error
        throw FileOperationError.readError(filePath, error instanceof Error ? error : undefined);
      }
    }, {
      context: 'file reading',
      exitOnError: false,
      logger
    });
  }

  /**
   * Safely read binary file
   */
  static async readBinaryFile(filePath: string): Promise<Buffer> {
    return withErrorHandling(async () => {
      try {
        return await fs.readFile(filePath);
      } catch (error) {
        // Check if file not found
        if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
          throw FileOperationError.notFound(filePath);
        }
        // Check if permission denied
        if ((error as NodeJS.ErrnoException).code === 'EACCES') {
          throw FileOperationError.permissionDenied(filePath);
        }
        // Generic read error
        throw FileOperationError.readError(filePath, error instanceof Error ? error : undefined);
      }
    }, {
      context: 'binary file reading',
      exitOnError: false,
      logger
    });
  }

  /**
   * Safely write file with proper permissions
   */
  static async writeFile(
    filePath: string, 
    content: string | Buffer, 
    options: FileOperationOptions = {}
  ): Promise<void> {
    const { mode = 0o600, encoding = 'utf-8', createDirectories = true } = options;
    
    return withErrorHandling(async () => {
      try {
        if (createDirectories) {
          await this.ensureDirectory(path.dirname(filePath));
        }

        if (typeof content === 'string') {
          await fs.writeFile(filePath, content, { mode, encoding });
        } else {
          await fs.writeFile(filePath, content, { mode });
        }
        
        logger.debug(`File written successfully: ${filePath}`);
      } catch (error) {
        // Check if permission denied
        if ((error as NodeJS.ErrnoException).code === 'EACCES') {
          throw FileOperationError.permissionDenied(filePath);
        }
        // Generic write error
        throw FileOperationError.writeError(filePath, error instanceof Error ? error : undefined);
      }
    }, {
      context: 'file writing',
      exitOnError: false,
      logger
    });
  }

  /**
   * Safely delete file
   */
  static async deleteFile(filePath: string): Promise<boolean> {
    return withErrorHandling(async () => {
      try {
        await fs.unlink(filePath);
        logger.debug(`File deleted: ${filePath}`);
        return true;
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
          logger.debug(`File does not exist: ${filePath}`);
          return false;
        }
        // Check if permission denied
        if ((error as NodeJS.ErrnoException).code === 'EACCES') {
          throw FileOperationError.permissionDenied(filePath);
        }
        // Generic delete error
        throw new FileOperationError(
          `Failed to delete file: ${filePath}`,
          `Failed to delete "${filePath}". Please check file permissions.`,
          filePath,
          { originalError: error instanceof Error ? error.message : String(error) }
        );
      }
    }, {
      context: 'file deletion',
      exitOnError: false,
      logger
    });
  }

  /**
   * Parse JSON file safely
   */
  static async readJsonFile<T = any>(filePath: string): Promise<T> {
    return withErrorHandling(async () => {
      try {
        const content = await this.readFile(filePath);
        try {
          return JSON.parse(content);
        } catch (error) {
          if (error instanceof SyntaxError) {
            throw new FileOperationError(
              `Invalid JSON in file: ${filePath}`,
              `The file "${filePath}" contains invalid JSON data and cannot be parsed.`,
              filePath,
              { originalError: error.message }
            );
          }
          throw error;
        }
      } catch (error) {
        // If it's already a FileOperationError, just rethrow it
        if (error instanceof FileOperationError) {
          throw error;
        }
        // Otherwise wrap it
        throw new FileOperationError(
          `Failed to read JSON file: ${filePath}`,
          `Failed to read JSON data from "${filePath}".`,
          filePath,
          { originalError: error instanceof Error ? error.message : String(error) }
        );
      }
    }, {
      context: 'JSON file reading',
      exitOnError: false,
      logger
    });
  }

  /**
   * Write JSON file safely with formatting
   */
  static async writeJsonFile(
    filePath: string, 
    data: any, 
    options: FileOperationOptions & { indent?: number } = {}
  ): Promise<void> {
    return withErrorHandling(async () => {
      const { indent = 2, ...fileOptions } = options;
      try {
        const jsonContent = JSON.stringify(data, null, indent);
        await this.writeFile(filePath, jsonContent, fileOptions);
      } catch (error) {
        // If it's already a FileOperationError, just rethrow it
        if (error instanceof FileOperationError) {
          throw error;
        }
        // Otherwise wrap it
        throw new FileOperationError(
          `Failed to write JSON file: ${filePath}`,
          `Failed to write JSON data to "${filePath}".`,
          filePath,
          { originalError: error instanceof Error ? error.message : String(error) }
        );
      }
    }, {
      context: 'JSON file writing',
      exitOnError: false,
      logger
    });
  }

  /**
   * Get file stats safely
   */
  static async getFileStats(filePath: string): Promise<import('fs').Stats | null> {
    return withErrorHandling(async () => {
      try {
        return await fs.stat(filePath);
      } catch {
        return null;
      }
    }, {
      context: 'file stats',
      exitOnError: false,
      logger
    });
  }

  /**
   * Check if path is a directory
   */
  static async isDirectory(dirPath: string): Promise<boolean> {
    return withErrorHandling(async () => {
      const stats = await this.getFileStats(dirPath);
      return stats?.isDirectory() ?? false;
    }, {
      context: 'directory check',
      exitOnError: false,
      logger
    });
  }

  /**
   * Get secure file path within user's home directory
   */
  static getSecurePath(...pathSegments: string[]): string {
    try {
      return path.join(os.homedir(), ...pathSegments);
    } catch (error) {
      logger.error('Error creating secure path:', error);
      // Fallback to current directory if home directory is not accessible
      return path.join(process.cwd(), ...pathSegments);
    }
  }

  /**
   * Backup file with timestamp
   */
  static async backupFile(filePath: string): Promise<string | null> {
    return withErrorHandling(async () => {
      if (!(await this.fileExists(filePath))) {
        return null;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${filePath}.backup.${timestamp}`;
      
      try {
        const content = await fs.readFile(filePath);
        await fs.writeFile(backupPath, content, { mode: 0o600 });
        logger.info(`File backed up to: ${backupPath}`);
        return backupPath;
      } catch (error) {
        throw new FileOperationError(
          `Failed to backup file: ${filePath}`,
          `Failed to create backup of "${filePath}". Please check disk space and permissions.`,
          filePath,
          { originalError: error instanceof Error ? error.message : String(error) }
        );
      }
    }, {
      context: 'file backup',
      exitOnError: false,
      logger
    });
  }

  /**
   * Securely read file with proper error handling
   * This is an alias for readFile with additional security checks
   */
  static async secureRead(filePath: string, encoding: BufferEncoding = 'utf-8'): Promise<string> {
    return this.readFile(filePath, encoding);
  }

  /**
   * Securely write file with proper permissions and error handling
   * This is an alias for writeFile with secure defaults
   */
  static async secureWrite(
    filePath: string, 
    content: string | Buffer, 
    options: FileOperationOptions = {}
  ): Promise<void> {
    // Ensure secure default permissions
    const secureOptions = {
      ...options,
      mode: options.mode ?? 0o600
    };
    return this.writeFile(filePath, content, secureOptions);
  }

  /**
   * Securely delete file with proper error handling
   * This is an alias for deleteFile with additional security checks
   */
  static async secureDelete(filePath: string): Promise<boolean> {
    return this.deleteFile(filePath);
  }

  /**
   * Read file with streaming for large files
   */
  static async readFileStream(filePath: string, chunkSize: number = 64 * 1024): Promise<string> {
    return withErrorHandling(async () => {
      const chunks: Buffer[] = [];
      const stream = require('fs').createReadStream(filePath, { highWaterMark: chunkSize });
      
      return new Promise<string>((resolve, reject) => {
        stream.on('data', (chunk: Buffer) => {
          chunks.push(chunk);
        });
        
        stream.on('end', () => {
          const content = Buffer.concat(chunks).toString('utf-8');
          // Clear chunks array for memory efficiency
          chunks.length = 0;
          resolve(content);
        });
        
        stream.on('error', (error: Error) => {
          reject(FileOperationError.readError(filePath, error));
        });
      });
    }, {
      context: 'streaming file read',
      exitOnError: false,
      logger
    });
  }

  /**
   * Write file with streaming for large content
   */
  static async writeFileStream(
    filePath: string, 
    content: string | Buffer, 
    options: FileOperationOptions = {}
  ): Promise<void> {
    const { mode = 0o600, createDirectories = true } = options;
    
    return withErrorHandling(async () => {
      if (createDirectories) {
        await this.ensureDirectory(path.dirname(filePath));
      }

      const stream = require('fs').createWriteStream(filePath, { mode });
      
      return new Promise<void>((resolve, reject) => {
        stream.on('finish', () => {
          logger.debug(`File written successfully (streaming): ${filePath}`);
          resolve();
        });
        
        stream.on('error', (error: Error) => {
          reject(FileOperationError.writeError(filePath, error));
        });
        
        stream.write(content);
        stream.end();
      });
    }, {
      context: 'streaming file write',
      exitOnError: false,
      logger
    });
  }

  /**
   * Batch file operations for better performance
   */
  static async batchFileOperations<T>(
    operations: Array<() => Promise<T>>,
    concurrency: number = 5
  ): Promise<T[]> {
    return withErrorHandling(async () => {
      const results: T[] = [];
      
      for (let i = 0; i < operations.length; i += concurrency) {
        const batch = operations.slice(i, i + concurrency);
        const batchResults = await Promise.allSettled(batch.map(op => op()));
        
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            logger.warn('Batch operation failed:', result.reason);
            throw result.reason;
          }
        }
      }
      
      return results;
    }, {
      context: 'batch file operations',
      exitOnError: false,
      logger
    });
  }

  /**
   * File operation with timeout
   */
  static async withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number = 30000,
    operationName: string = 'file operation'
  ): Promise<T> {
    return PerformanceUtils.withTimeout(
      operation,
      timeoutMs,
      `${operationName} timed out after ${timeoutMs}ms`
    );
  }

  /**
   * Optimized file copy with progress tracking
   */
  static async copyFile(
    sourcePath: string,
    destPath: string,
    options: FileOperationOptions & { 
      overwrite?: boolean;
      preserveTimestamps?: boolean;
      onProgress?: (bytesWritten: number, totalBytes: number) => void;
    } = {}
  ): Promise<void> {
    const { mode = 0o600, createDirectories = true, overwrite = false, preserveTimestamps = false, onProgress } = options;
    
    return withErrorHandling(async () => {
      // Check if source exists
      if (!(await this.fileExists(sourcePath))) {
        throw FileOperationError.notFound(sourcePath);
      }

      // Check if destination exists and overwrite is not allowed
      if (!overwrite && await this.fileExists(destPath)) {
        throw new FileOperationError(
          `Destination file already exists: ${destPath}`,
          `File "${destPath}" already exists. Use overwrite option to replace it.`,
          destPath
        );
      }

      if (createDirectories) {
        await this.ensureDirectory(path.dirname(destPath));
      }

      // Get source file stats for progress tracking
      const sourceStats = await this.getFileStats(sourcePath);
      const totalBytes = sourceStats?.size || 0;
      let bytesWritten = 0;

      // Use streaming for large files
      if (totalBytes > 1024 * 1024) { // 1MB threshold
        const readStream = require('fs').createReadStream(sourcePath);
        const writeStream = require('fs').createWriteStream(destPath, { mode });

        return new Promise<void>((resolve, reject) => {
          readStream.on('data', (chunk: Buffer) => {
            bytesWritten += chunk.length;
            if (onProgress) {
              onProgress(bytesWritten, totalBytes);
            }
          });

          readStream.on('error', reject);
          writeStream.on('error', reject);
          writeStream.on('finish', async () => {
            try {
              if (preserveTimestamps && sourceStats) {
                await fs.utimes(destPath, sourceStats.atime, sourceStats.mtime);
              }
              resolve();
            } catch (error) {
              reject(error);
            }
          });

          readStream.pipe(writeStream);
        });
      } else {
        // Use simple copy for small files
        await fs.copyFile(sourcePath, destPath);
        await fs.chmod(destPath, mode);
        
        if (preserveTimestamps && sourceStats) {
          await fs.utimes(destPath, sourceStats.atime, sourceStats.mtime);
        }
        
        if (onProgress) {
          onProgress(totalBytes, totalBytes);
        }
      }

      logger.debug(`File copied successfully: ${sourcePath} -> ${destPath}`);
    }, {
      context: 'file copying',
      exitOnError: false,
      logger
    });
  }
}