/**
 * Main barrel export for the refactored Confluence page update project
 */

// Export types
export * from './types';

// Export utilities
export * from './utils';

// Export configuration
export * from './config/app-config';
export * from './config/messages';

// Export services
export { SecureCredentialsManager } from './services/secure-credentials-manager';
export { SecureCookieManager } from './services/secure-cookie-manager';
export { lazyPuppeteerManager, LazyPuppeteerManager } from './services/lazy-puppeteer-manager';
export { createHttpClient, createConfluenceHttpClient, httpClient } from './services/http-client';

// Export CLI modules
export * from './cli';

// Export errors
export * from './errors';