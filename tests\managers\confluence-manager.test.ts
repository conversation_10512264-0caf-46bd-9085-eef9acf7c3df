/**
 * Tests for Confluence Manager
 */

import { ConfluenceManager } from '../../src/managers/confluence-manager';
import { SecureCredentialsManager } from '../../src/services/secure-credentials-manager';
import { SecureCookieManager } from '../../src/services/secure-cookie-manager';
import { LazyPuppeteerManager } from '../../src/services/lazy-puppeteer-manager';
import { ValidationError, NetworkError, AuthenticationError } from '../../src/errors';
import { logger } from '../../src/utils/logger';
import { PerformanceUtils } from '../../src/utils/performance-utils';

// Mock dependencies
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/services/secure-cookie-manager');
jest.mock('../../src/services/lazy-puppeteer-manager');
jest.mock('../../src/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));
jest.mock('../../src/utils/performance-utils');

describe('ConfluenceManager', () => {
  let manager: ConfluenceManager;
  let mockCredentialsManager: jest.Mocked<SecureCredentialsManager>;
  let mockCookieManager: jest.Mocked<SecureCookieManager>;
  let mockPuppeteerManager: jest.Mocked<LazyPuppeteerManager>;
  let mockBrowser: any;
  let mockPage: any;

  const baseUrl = 'https://test.atlassian.net';

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create mock instances
    mockCredentialsManager = {
      getCredentialByBaseUrl: jest.fn(),
      initialize: jest.fn(),
      cleanup: jest.fn()
    } as any;

    mockCookieManager = {
      hasCookiesForBaseUrl: jest.fn(),
      getCookiesForBaseUrl: jest.fn(),
      saveCookies: jest.fn(),
      initialize: jest.fn(),
      cleanup: jest.fn()
    } as any;

    mockBrowser = {
      newPage: jest.fn(),
      close: jest.fn(),
      pages: jest.fn().mockResolvedValue([])
    };

    mockPage = {
      goto: jest.fn(),
      setCookie: jest.fn(),
      url: jest.fn(),
      type: jest.fn(),
      click: jest.fn(),
      waitForNavigation: jest.fn(),
      evaluate: jest.fn(),
      cookies: jest.fn(),
      close: jest.fn()
    };

    mockPuppeteerManager = {
      launchBrowser: jest.fn().mockResolvedValue(mockBrowser),
      createPage: jest.fn().mockResolvedValue(mockPage),
      navigateToUrl: jest.fn(),
      closePage: jest.fn(),
      closeBrowser: jest.fn(),
      cleanup: jest.fn(),
      getStats: jest.fn()
    } as any;

    // Set up constructor mocks
    (SecureCredentialsManager as jest.Mock).mockImplementation(() => mockCredentialsManager);
    (SecureCookieManager as jest.Mock).mockImplementation(() => mockCookieManager);
    (LazyPuppeteerManager as jest.Mock).mockImplementation(() => mockPuppeteerManager);

    manager = new ConfluenceManager(
      baseUrl,
      mockCredentialsManager,
      mockCookieManager,
      mockPuppeteerManager
    );
  });

  describe('constructor', () => {
    it('should create instance with valid baseUrl', () => {
      expect(manager).toBeInstanceOf(ConfluenceManager);
      expect(logger.debug).toHaveBeenCalledWith('ConfluenceManager initialized');
    });

    it('should throw ValidationError for empty baseUrl', () => {
      expect(() => {
        new ConfluenceManager(
          '',
          mockCredentialsManager,
          mockCookieManager,
          mockPuppeteerManager
        );
      }).toThrow(ValidationError);
    });

    it('should throw ValidationError for null baseUrl', () => {
      expect(() => {
        new ConfluenceManager(
          null as any,
          mockCredentialsManager,
          mockCookieManager,
          mockPuppeteerManager
        );
      }).toThrow(ValidationError);
    });
  });

  describe('updatePage', () => {
    const pageId = '123456';
    const content = '<p>Test content</p>';
    const title = 'Test Page';

    beforeEach(() => {
      // Mock successful authentication
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(true);
      mockCookieManager.getCookiesForBaseUrl.mockReturnValue([
        {
          name: 'session',
          value: 'abc123',
          domain: '.atlassian.net',
          savedAt: Date.now(),
          baseUrl: baseUrl
        }
      ]);

      // Mock successful page navigation
      mockPage.url.mockReturnValue('https://test.atlassian.net/pages/editpage.action?pageId=123456');
    });

    it('should update page successfully', async () => {
      const result = await manager.updatePage(pageId, content, title);

      expect(result).toBe(pageId);
      expect(mockPuppeteerManager.launchBrowser).toHaveBeenCalled();
      expect(mockPuppeteerManager.createPage).toHaveBeenCalledWith(mockBrowser);
      expect(mockPage.setCookie).toHaveBeenCalled();
      expect(mockPuppeteerManager.navigateToUrl).toHaveBeenCalledWith(
        mockPage,
        `${baseUrl}/pages/editpage.action?pageId=${pageId}`
      );
      expect(mockPage.evaluate).toHaveBeenCalledWith(expect.any(Function), content);
      expect(mockPage.evaluate).toHaveBeenCalledWith(expect.any(Function), title);
      expect(mockPage.click).toHaveBeenCalledWith('#save-button');
      expect(mockPage.waitForNavigation).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Page updated successfully');
    });

    it('should update page without title', async () => {
      const result = await manager.updatePage(pageId, content);

      expect(result).toBe(pageId);
      expect(mockPage.evaluate).toHaveBeenCalledWith(expect.any(Function), content);
      // Should not call evaluate for title
      expect(mockPage.evaluate).toHaveBeenCalledTimes(1);
    });

    it('should throw AuthenticationError when redirected to login', async () => {
      mockPage.url.mockReturnValue('https://test.atlassian.net/login.action');

      await expect(manager.updatePage(pageId, content)).rejects.toThrow(AuthenticationError);
      expect(logger.info).toHaveBeenCalledWith(`Updating Confluence page: ${pageId}`);
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Timeout');
      timeoutError.name = 'TimeoutError';
      mockPage.click.mockRejectedValue(timeoutError);

      await expect(manager.updatePage(pageId, content)).rejects.toThrow(NetworkError);
    });

    it('should clean up resources on success', async () => {
      await manager.updatePage(pageId, content);

      expect(mockPuppeteerManager.closePage).toHaveBeenCalledWith(mockPage);
      expect(mockPuppeteerManager.closeBrowser).toHaveBeenCalledWith(mockBrowser);
    });

    it('should clean up resources on error', async () => {
      mockPage.click.mockRejectedValue(new Error('Test error'));

      await expect(manager.updatePage(pageId, content)).rejects.toThrow();

      expect(mockPuppeteerManager.closePage).toHaveBeenCalledWith(mockPage);
      expect(mockPuppeteerManager.closeBrowser).toHaveBeenCalledWith(mockBrowser);
    });

    it('should perform authentication when no cookies exist', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(false);
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl,
        username: 'testuser',
        password: 'testpass',
        spaceKey: 'TEST',
        token: '',
        puppeteerLogin: true
      });

      // Mock successful login
      mockPage.url
        .mockReturnValueOnce('https://test.atlassian.net/dashboard.action') // After login (successful)
        .mockReturnValueOnce('https://test.atlassian.net/pages/editpage.action?pageId=123456'); // During page edit

      mockPage.cookies.mockResolvedValue([
        { name: 'session', value: 'new-session', domain: '.atlassian.net', path: '/', secure: true, httpOnly: true }
      ]);

      await manager.updatePage(pageId, content);

      expect(mockCredentialsManager.getCredentialByBaseUrl).toHaveBeenCalledWith(baseUrl);
      expect(mockPuppeteerManager.navigateToUrl).toHaveBeenCalledWith(
        mockPage,
        `${baseUrl}/login.action`
      );
      expect(mockPage.type).toHaveBeenCalledWith('#username', 'testuser');
      expect(mockPage.type).toHaveBeenCalledWith('#password', 'testpass');
      expect(mockPage.click).toHaveBeenCalledWith('#login-submit');
      expect(mockCookieManager.saveCookies).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Authentication successful');
    });

    it('should throw AuthenticationError when no credentials found', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(false);
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue(undefined);

      await expect(manager.updatePage(pageId, content)).rejects.toThrow(AuthenticationError);
    });

    it('should throw AuthenticationError when credentials are incomplete', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(false);
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl,
        username: 'testuser',
        password: '', // Missing password
        spaceKey: 'TEST',
        token: '',
        puppeteerLogin: true
      });

      await expect(manager.updatePage(pageId, content)).rejects.toThrow(AuthenticationError);
    });

    it('should throw AuthenticationError when login fails', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(false);
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl,
        username: 'testuser',
        password: 'testpass',
        spaceKey: 'TEST',
        token: '',
        puppeteerLogin: true
      });

      // Mock failed login (still on login page)
      mockPage.url.mockReturnValue('https://test.atlassian.net/login.action?error=true');

      await expect(manager.updatePage(pageId, content)).rejects.toThrow(AuthenticationError);
      // The AuthenticationError is thrown directly, not logged as an error
    });

    it('should handle authentication errors during login', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(false);
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl,
        username: 'testuser',
        password: 'testpass',
        spaceKey: 'TEST',
        token: '',
        puppeteerLogin: true
      });

      mockPage.type.mockRejectedValue(new Error('Network error'));

      await expect(manager.updatePage(pageId, content)).rejects.toThrow(AuthenticationError);
      expect(logger.error).toHaveBeenCalledWith('Authentication failed', expect.any(Error));
    });
  });

  describe('ensureAuthentication', () => {
    it('should use existing cookies when available', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(true);

      await manager['ensureAuthentication']();

      expect(logger.debug).toHaveBeenCalledWith('Using existing authentication cookies');
      expect(mockCredentialsManager.getCredentialByBaseUrl).not.toHaveBeenCalled();
    });

    it('should perform authentication when no cookies exist', async () => {
      mockCookieManager.hasCookiesForBaseUrl.mockReturnValue(false);
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl,
        username: 'testuser',
        password: 'testpass',
        spaceKey: 'TEST',
        token: '',
        puppeteerLogin: true
      });

      mockPage.url
        .mockReturnValueOnce('https://test.atlassian.net/dashboard.action');

      mockPage.cookies.mockResolvedValue([
        { name: 'session', value: 'new-session', domain: '.atlassian.net', path: '/', secure: true, httpOnly: true }
      ]);

      await manager['ensureAuthentication']();

      expect(logger.info).toHaveBeenCalledWith('No valid cookies found, performing authentication');
      expect(mockCredentialsManager.getCredentialByBaseUrl).toHaveBeenCalledWith(baseUrl);
    });
  });

  describe('authenticate', () => {
    beforeEach(() => {
      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl,
        username: 'testuser',
        password: 'testpass',
        spaceKey: 'TEST',
        token: '',
        puppeteerLogin: true
      });
    });

    it('should authenticate successfully', async () => {
      mockPage.url
        .mockReturnValueOnce('https://test.atlassian.net/dashboard.action');

      mockPage.cookies.mockResolvedValue([
        { name: 'session', value: 'new-session', domain: '.atlassian.net', path: '/', secure: true, httpOnly: true }
      ]);

      await manager['authenticate']();

      expect(mockPuppeteerManager.navigateToUrl).toHaveBeenCalledWith(
        mockPage,
        `${baseUrl}/login.action`
      );
      expect(mockPage.type).toHaveBeenCalledWith('#username', 'testuser');
      expect(mockPage.type).toHaveBeenCalledWith('#password', 'testpass');
      expect(mockPage.click).toHaveBeenCalledWith('#login-submit');
      expect(mockCookieManager.saveCookies).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Authentication successful');
    });

    it('should clean up resources after authentication', async () => {
      mockPage.url
        .mockReturnValueOnce('https://test.atlassian.net/dashboard.action');

      mockPage.cookies.mockResolvedValue([]);

      await manager['authenticate']();

      expect(mockPuppeteerManager.closePage).toHaveBeenCalledWith(mockPage);
      expect(mockPuppeteerManager.closeBrowser).toHaveBeenCalledWith(mockBrowser);
    });

    it('should clean up resources on authentication error', async () => {
      mockPage.type.mockRejectedValue(new Error('Network error'));

      await expect(manager['authenticate']()).rejects.toThrow(AuthenticationError);

      expect(mockPuppeteerManager.closePage).toHaveBeenCalledWith(mockPage);
      expect(mockPuppeteerManager.closeBrowser).toHaveBeenCalledWith(mockBrowser);
    });
  });
});
