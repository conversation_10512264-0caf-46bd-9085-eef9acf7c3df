/**
 * Tests for validation utilities
 */

import { ValidationUtils } from '../../src/utils/validation';
import { ConfluenceCredential } from '../../src/types';

describe('ValidationUtils', () => {
  describe('isValidUrl', () => {
    it('should return true for valid URLs', () => {
      expect(ValidationUtils.isValidUrl('https://example.com')).toBe(true);
      expect(ValidationUtils.isValidUrl('http://localhost:8080')).toBe(true);
      expect(ValidationUtils.isValidUrl('https://test.atlassian.net/wiki')).toBe(true);
    });

    it('should return false for invalid URLs', () => {
      expect(ValidationUtils.isValidUrl('not-a-url')).toBe(false);
      // Note: ftp:// is actually a valid URL, just not http/https
      expect(ValidationUtils.isValidUrl('')).toBe(false);
      expect(ValidationUtils.isValidUrl('just-text')).toBe(false);
    });
  });

  describe('isValidConfluenceUrl', () => {
    it('should validate correct Confluence URLs', () => {
      const result = ValidationUtils.isValidConfluenceUrl('https://test.atlassian.net');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate HTTP URLs', () => {
      const result = ValidationUtils.isValidConfluenceUrl('http://localhost:8080');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty or null URLs', () => {
      const result1 = ValidationUtils.isValidConfluenceUrl('');
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Base URL is required');

      const result2 = ValidationUtils.isValidConfluenceUrl(null as any);
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Base URL is required');
    });

    it('should reject invalid URL formats', () => {
      const result = ValidationUtils.isValidConfluenceUrl('not-a-url');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Base URL must be a valid URL');
    });

    it('should reject URLs without http/https protocol', () => {
      const result = ValidationUtils.isValidConfluenceUrl('ftp://example.com');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Base URL must start with http:// or https://');
    });
  });

  describe('isValidSpaceKey', () => {
    it('should validate correct space keys', () => {
      const result1 = ValidationUtils.isValidSpaceKey('TEST');
      expect(result1.isValid).toBe(true);
      expect(result1.errors).toHaveLength(0);

      const result2 = ValidationUtils.isValidSpaceKey('My_Space_123');
      expect(result2.isValid).toBe(true);
      expect(result2.errors).toHaveLength(0);
    });

    it('should reject empty or null space keys', () => {
      const result1 = ValidationUtils.isValidSpaceKey('');
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Space key is required');

      const result2 = ValidationUtils.isValidSpaceKey(null as any);
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Space key is required');
    });

    it('should reject space keys with special characters', () => {
      const result = ValidationUtils.isValidSpaceKey('TEST-SPACE');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Space key should contain only letters, numbers, and underscores');
    });

    it('should reject space keys that are too long', () => {
      const longKey = 'A'.repeat(256);
      const result = ValidationUtils.isValidSpaceKey(longKey);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Space key must be between 1 and 255 characters');
    });
  });

  describe('isValidCredentialName', () => {
    it('should validate correct credential names', () => {
      const result = ValidationUtils.isValidCredentialName('My Confluence Instance');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty or null names', () => {
      const result1 = ValidationUtils.isValidCredentialName('');
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Credential name is required');

      const result2 = ValidationUtils.isValidCredentialName('   ');
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Credential name cannot be empty');
    });

    it('should reject names that are too long', () => {
      const longName = 'A'.repeat(101);
      const result = ValidationUtils.isValidCredentialName(longName);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Credential name must be 100 characters or less');
    });
  });

  describe('isValidApiToken', () => {
    it('should validate correct API tokens', () => {
      const result = ValidationUtils.isValidApiToken('ATATT3xFfGF0T4JNjGmWVLxLBxLBxLBxLBxL');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty or null tokens', () => {
      const result1 = ValidationUtils.isValidApiToken('');
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('API token is required');

      const result2 = ValidationUtils.isValidApiToken(null as any);
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('API token is required');
    });

    it('should reject tokens that are too short', () => {
      const result = ValidationUtils.isValidApiToken('short');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API token appears to be too short');
    });
  });

  describe('isValidUsername', () => {
    it('should validate correct usernames', () => {
      const result1 = ValidationUtils.isValidUsername('john.doe');
      expect(result1.isValid).toBe(true);
      expect(result1.errors).toHaveLength(0);

      const result2 = ValidationUtils.isValidUsername('<EMAIL>');
      expect(result2.isValid).toBe(true);
      expect(result2.errors).toHaveLength(0);
    });

    it('should reject empty or null usernames', () => {
      const result1 = ValidationUtils.isValidUsername('');
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Username is required');

      const result2 = ValidationUtils.isValidUsername('   ');
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Username cannot be empty');
    });

    it('should reject invalid email formats when username looks like email', () => {
      const result = ValidationUtils.isValidUsername('invalid@email');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Username appears to be an email but is not valid');
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(ValidationUtils.isValidEmail('<EMAIL>')).toBe(true);
      expect(ValidationUtils.isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(ValidationUtils.isValidEmail('invalid-email')).toBe(false);
      expect(ValidationUtils.isValidEmail('user@')).toBe(false);
      expect(ValidationUtils.isValidEmail('@domain.com')).toBe(false);
      expect(ValidationUtils.isValidEmail('user@domain')).toBe(false);
    });
  });

  describe('isValidPassword', () => {
    it('should validate correct passwords', () => {
      const result = ValidationUtils.isValidPassword('password123');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty or null passwords', () => {
      const result1 = ValidationUtils.isValidPassword('');
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Password is required');

      const result2 = ValidationUtils.isValidPassword(null as any);
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Password is required');
    });

    it('should reject passwords that are too short', () => {
      const result = ValidationUtils.isValidPassword('short');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should reject passwords that are too long', () => {
      const longPassword = 'A'.repeat(129);
      const result = ValidationUtils.isValidPassword(longPassword);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be 128 characters or less');
    });
  });

  describe('validateCredential', () => {
    const validCredential: ConfluenceCredential = {
      name: 'Test Instance',
      baseUrl: 'https://test.atlassian.net',
      spaceKey: 'TEST',
      token: 'ATATT3xFfGF0T4JNjGmWVLxL',
      puppeteerLogin: false,
      username: '<EMAIL>',
      password: 'password123'
    };

    it('should validate a complete valid credential', () => {
      const result = ValidationUtils.validateCredential(validCredential);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate credential with browser login', () => {
      const browserCredential = {
        ...validCredential,
        puppeteerLogin: true,
        token: undefined
      };
      const result = ValidationUtils.validateCredential(browserCredential);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject credential missing API token for API authentication', () => {
      const invalidCredential = {
        ...validCredential,
        token: undefined,
        puppeteerLogin: false
      };
      const result = ValidationUtils.validateCredential(invalidCredential);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API token is required when not using browser login');
    });

    it('should accumulate multiple validation errors', () => {
      const invalidCredential = {
        name: '',
        baseUrl: 'invalid-url',
        spaceKey: 'INVALID-KEY',
        token: 'short'
      };
      const result = ValidationUtils.validateCredential(invalidCredential);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('validateCredentials', () => {
    it('should validate array of valid credentials', () => {
      const credentials = [
        {
          name: 'Test 1',
          baseUrl: 'https://test1.atlassian.net',
          spaceKey: 'TEST1',
          token: 'ATATT3xFfGF0T4JNjGmWVLxL'
        },
        {
          name: 'Test 2',
          baseUrl: 'https://test2.atlassian.net',
          spaceKey: 'TEST2',
          token: 'ATATT3xFfGF0T4JNjGmWVLxL'
        }
      ];
      const result = ValidationUtils.validateCredentials(credentials);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-array input', () => {
      const result = ValidationUtils.validateCredentials('not-an-array' as any);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should report errors for invalid credentials with index', () => {
      const credentials = [
        {
          name: 'Valid',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          token: 'ATATT3xFfGF0T4JNjGmWVLxL'
        },
        {
          name: '',
          baseUrl: 'invalid-url',
          spaceKey: 'TEST'
        }
      ];
      const result = ValidationUtils.validateCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Credential 2:'))).toBe(true);
    });
  });

  describe('sanitizeCredentialName', () => {
    it('should sanitize credential names for safe file operations', () => {
      expect(ValidationUtils.sanitizeCredentialName('My Test Instance!')).toBe('my-test-instance');
      expect(ValidationUtils.sanitizeCredentialName('Test@#$%^&*()Instance')).toBe('testinstance');
      expect(ValidationUtils.sanitizeCredentialName('  Multiple   Spaces  ')).toBe('-multiple-spaces-');
    });

    it('should limit length to 50 characters', () => {
      const longName = 'A'.repeat(100);
      const result = ValidationUtils.sanitizeCredentialName(longName);
      expect(result.length).toBe(50);
    });
  });

  describe('isDuplicateCredential', () => {
    it('should detect duplicate credentials by baseUrl', () => {
      const cred1: ConfluenceCredential = {
        name: 'Test 1',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST1',
        token: 'token1',
        puppeteerLogin: false,
        username: 'user1',
        password: 'pass1'
      };

      const cred2: ConfluenceCredential = {
        name: 'Test 2',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST2',
        token: 'token2',
        puppeteerLogin: false,
        username: 'user2',
        password: 'pass2'
      };

      expect(ValidationUtils.isDuplicateCredential(cred1, cred2)).toBe(true);
    });

    it('should handle case-insensitive URL comparison', () => {
      const cred1: ConfluenceCredential = {
        name: 'Test 1',
        baseUrl: 'https://TEST.atlassian.net',
        spaceKey: 'TEST1',
        token: 'token1',
        puppeteerLogin: false,
        username: 'user1',
        password: 'pass1'
      };

      const cred2: ConfluenceCredential = {
        name: 'Test 2',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST2',
        token: 'token2',
        puppeteerLogin: false,
        username: 'user2',
        password: 'pass2'
      };

      expect(ValidationUtils.isDuplicateCredential(cred1, cred2)).toBe(true);
    });

    it('should not detect different URLs as duplicates', () => {
      const cred1: ConfluenceCredential = {
        name: 'Test 1',
        baseUrl: 'https://test1.atlassian.net',
        spaceKey: 'TEST1',
        token: 'token1',
        puppeteerLogin: false,
        username: 'user1',
        password: 'pass1'
      };

      const cred2: ConfluenceCredential = {
        name: 'Test 2',
        baseUrl: 'https://test2.atlassian.net',
        spaceKey: 'TEST2',
        token: 'token2',
        puppeteerLogin: false,
        username: 'user2',
        password: 'pass2'
      };

      expect(ValidationUtils.isDuplicateCredential(cred1, cred2)).toBe(false);
    });
  });
});
