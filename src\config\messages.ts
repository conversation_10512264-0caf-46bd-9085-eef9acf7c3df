/**
 * Centralized user-facing messages and error text for potential internationalization
 */

export interface MessageCatalog {
  errors: {
    passwordRequired: string;
    invalidPassword: string;
    keyFileNotFound: string;
    encryptionKeyNotInitialized: string;
    invalidCredentialFormat: string;
    missingRequiredFields: string;
    fileOperationFailed: string;
    networkError: string;
    authenticationFailed: string;
    corruptedData: string;
  };
  info: {
    credentialsImported: (count: number) => string;
    credentialsExported: (count: number, path: string) => string;
    cookiesSaved: (count: number, domain: string) => string;
    cookiesCleared: string;
    masterKeyEncrypted: string;
    masterKeyMigrated: string;
    initializationComplete: string;
  };
  warnings: {
    plainKeyFormat: string;
    sensitiveDataExported: string;
    legacyFormat: string;
  };
  prompts: {
    enterPassword: string;
    confirmPassword: string;
    selectCredential: string;
  };
}

export const messages: MessageCatalog = {
  errors: {
    passwordRequired: 'Password required to decrypt master key',
    invalidPassword: 'Invalid password or corrupted key file',
    keyFileNotFound: 'Master key not found. Please initialize credentials first using SecureCredentialsManager.',
    encryptionKeyNotInitialized: 'Encryption key not initialized',
    invalidCredentialFormat: 'Invalid credentials format: expected an array',
    missingRequiredFields: 'Invalid credential: missing required fields',
    fileOperationFailed: 'File operation failed',
    networkError: 'Network request failed',
    authenticationFailed: 'Authentication failed',
    corruptedData: 'Data appears to be corrupted'
  },
  info: {
    credentialsImported: (count: number) => `Successfully imported ${count} credentials`,
    credentialsExported: (count: number, path: string) => `Successfully exported ${count} credentials to ${path}`,
    cookiesSaved: (count: number, domain: string) => `${count} cookies encrypted and saved for ${domain}`,
    cookiesCleared: 'All cookies cleared',
    masterKeyEncrypted: 'Master key has been encrypted and migrated to new format.',
    masterKeyMigrated: 'Master key migrated to encrypted format',
    initializationComplete: 'Secure manager initialized successfully'
  },
  warnings: {
    plainKeyFormat: 'Warning: Master key is stored in plain format. Consider providing a password to encrypt it.',
    sensitiveDataExported: 'Warning: The exported file contains sensitive information in plain text. Please store it securely and delete it after use.',
    legacyFormat: 'Warning: Using legacy format. Consider migrating to encrypted format.'
  },
  prompts: {
    enterPassword: 'Enter password: ',
    confirmPassword: 'Confirm password: ',
    selectCredential: 'Select a credential:'
  }
};