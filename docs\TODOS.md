# TODOs for Confluence Page Update Project

This file tracks future changes, improvements, and adaptations for the project. Items are prioritized as High, Medium, or Low. Add new tasks as needed, and mark completed ones with [x].

## Pending Tasks

### High Priority

### Medium Priority

### Low Priority

## Completed Tasks

- [x] **TODO-9H2F** - Fix password display in `confluence-credentials` command.
  - **Description**: Currently when entering the master password, both the actual characters and asterisks are displayed (e.g., "l\*e\*n\*i\*1\*2\*3\*4\*"). Modify the password input handling to only display asterisks without showing the actual characters.
  - **Rationale**: Improves security by preventing password exposure during entry and provides expected masking behavior.
  - **Files to Modify**: credential-manager-cli.ts (or relevant file handling password input).
  - **Estimated Effort**: Low (1-2 hours).
  - **Completion Notes**: Fixed password input to show only asterisks using backspace-overwrite approach in Windows PowerShell. Modified askPassword function in credential-manager-cli.ts to properly mask password input without displaying actual characters.

- [x] **TODO-6K3L** - Fix premature exit in `confluence-credentials` command.
  - **Description**: Currently the script exits immediately upon launch without allowing user input. Modify the script to properly wait for user interaction and input before proceeding.
  - **Rationale**: Ensures users can actually interact with the credential manager and perform necessary operations.
  - **Files to Modify**: credential-manager-cli.ts and related command execution files.
  - **Estimated Effort**: Low (1-2 hours).
  - **Completion Notes**: Replaced process.exit() calls with graceful error handling, added password retry mechanism (3 attempts), improved error messages. Modified main function and error handlers in credential-manager-cli.ts to prevent premature exit and allow proper user interaction.

- [x] **TODO-5R1T** - Migrate all JavaScript files to TypeScript for consistency.
  - **Description**: The project currently mixes JS and TS files (e.g., update_confluence_page.js is JS while others are TS). Convert all JS to TS, update imports, and ensure tsconfig covers everything.
  - **Rationale**: Standardizing to TypeScript improves type safety, maintainability, and aligns with the project's TS-based structure.
  - **Files to Modify**: update_confluence_page.js (convert to .ts), potentially others; adjust package.json scripts and tsconfig.json.
  - **Estimated Effort**: Medium (2-3 hours, including testing).
  - **Completion Notes**: Converted update_confluence_page.js and test-cookies.js to .ts, fixed TS errors, updated package.json scripts and bin. Run `npm run build` to verify.

- [x] **TODO-8F3A** - Implement strong password-based encryption for the master key (`.key`) and encrypted credentials ( `credentials.enc`) files.
  - **Description**: Currently, the master key is stored in plain form with file permissions. Add a user-provided passphrase to encrypt the key file itself, requiring password entry on load/init for better protection against local file access.
  - **Rationale**: Enhances security if files are compromised (e.g., via backups or shared access).
  - **Files to Modify**: secure-credentials-manager.ts (add PBKDF2-based encryption for key), credential-manager-cli.ts (prompt for password).
  - **Estimated Effort**: Medium (2-4 hours).
  - **Completion Notes**: Implemented password-based encryption using PBKDF2 for master key protection. Added password prompting in CLI and secure key derivation. Master key is now encrypted with user passphrase and requires password entry for access.

- [x] **TODO-2K9P** - Add export functionality for stored credentials.
  - **Description**: Implement a CLI option to decrypt and export credentials from credentials.enc (using .key) to a plain JSON file for backup or migration.
  - **Rationale**: Allows safe exporting of configurations while maintaining security.
  - **Files to Modify**: credential-manager-cli.ts (add export option), secure-credentials-manager.ts (add export method).
  - **Estimated Effort**: Low (1-2 hours).
  - **Completion Notes**: Added `--export` CLI option to decrypt and export stored credentials to JSON format. Implemented secure export method that requires password authentication and outputs credentials in readable format for backup/migration purposes.

- [x] **TODO-7M4Q** - Adjust import to support selecting any JSON file instead of defaulting to config.json.
  - **Description**: Modify the import CLI to allow specifying any file path interactively or via argument, rather than assuming config.json.
  - **Rationale**: Improves flexibility for importing from different locations/sources.
  - **Files to Modify**: credential-manager-cli.ts (update importFromConfigJson to accept dynamic paths).
  - **Estimated Effort**: Low (1 hour).
  - **Completion Notes**: Modified `--import` CLI option to accept any JSON file path as argument. Updated import functionality to support flexible file selection instead of hardcoded config.json, improving usability for different import sources.

- [x] **TODO-3P6B** - Implement cookie encryption and decryption using the same encryption method as credentials.
  - **Description**: Currently, cookies may be stored in a less secure format. Modify the cookie handling to use the same encryption/decryption mechanism used for other sensitive data.
  - **Rationale**: Ensures consistent security across all sensitive data including authentication cookies.
  - **Files to Modify**: cookie-manager.ts (or relevant file handling cookies), secure-credentials-manager.ts (to reuse encryption methods).
  - **Estimated Effort**: Medium (2-3 hours).
  - **Completion Notes**: Implemented secure cookie encryption using the same AES-256-GCM encryption method as credentials. Created secure-cookie-manager.ts to handle encrypted cookie storage and retrieval, ensuring consistent security across all sensitive data.
