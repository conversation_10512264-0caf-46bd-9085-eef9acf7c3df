#!/usr/bin/env node
/**
 * Simple test script to verify performance optimizations are working
 */

import { PerformanceUtils } from './performance-utils';
import { LazyPuppeteerManager } from '../services/lazy-puppeteer-manager';

// Create an instance of the lazy puppeteer manager
const lazyPuppeteerManager = new LazyPuppeteerManager();

export async function testPerformanceOptimizations(): Promise<void> {
    console.log('🚀 Testing Performance Optimizations...\n');
    
    // Test 1: Memory usage monitoring
    console.log('1. Testing memory usage monitoring:');
    PerformanceUtils.logMemoryUsage('test-start');
    
    // Test 2: Lazy module loading
    console.log('\n2. Testing lazy module loading:');
    console.log('Puppeteer module loaded:', lazyPuppeteerManager.getStats().moduleLoaded);
    
    // Test 3: Cleanup registration
    console.log('\n3. Testing cleanup registration:');
    const testCleanup = {
        cleanup: async (): Promise<void> => {
            console.log('Test cleanup executed');
        }
    };
    PerformanceUtils.registerCleanup(testCleanup);
    console.log('Cleanup task registered successfully');
    
    // Test 4: Timeout wrapper
    console.log('\n4. Testing timeout wrapper:');
    try {
        const result = await PerformanceUtils.withTimeout(
            () => new Promise<string>(resolve => setTimeout(() => resolve('Success!'), 100)),
            1000,
            'Test operation'
        );
        console.log('Timeout test result:', result);
    } catch (error) {
        console.error('Timeout test failed:', error instanceof Error ? error.message : String(error));
    }
    
    // Test 5: Debounce function
    console.log('\n5. Testing debounce function:');
    let debounceCount = 0;
    const debouncedFn = PerformanceUtils.debounce(() => {
        debounceCount++;
        console.log('Debounced function executed, count:', debounceCount);
    }, 100);
    
    // Call multiple times quickly
    debouncedFn();
    debouncedFn();
    debouncedFn();
    
    // Wait for debounce to execute
    await new Promise(resolve => setTimeout(resolve, 150));
    
    // Test 6: Puppeteer manager stats
    console.log('\n6. Testing Puppeteer manager stats:');
    const stats = lazyPuppeteerManager.getStats();
    console.log('Puppeteer stats:', stats);
    
    // Test 7: Secure wipe
    console.log('\n7. Testing secure memory wipe:');
    const testBuffer = Buffer.from('sensitive data');
    console.log('Before wipe:', testBuffer.toString());
    PerformanceUtils.secureWipe(testBuffer);
    console.log('After wipe (should be zeros):', testBuffer.toString('hex'));
    
    // Test 8: Force garbage collection
    console.log('\n8. Testing garbage collection:');
    PerformanceUtils.forceGarbageCollection();
    console.log('Garbage collection attempted');
    
    PerformanceUtils.logMemoryUsage('test-end');
    
    console.log('\n✅ All performance optimization tests completed successfully!');
    
    // Cleanup
    await PerformanceUtils.executeCleanup();
}

// Only run directly if this file is being executed directly
if (require.main === module) {
    // Run the test
    testPerformanceOptimizations().catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}