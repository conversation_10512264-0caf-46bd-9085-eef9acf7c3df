/**
 * Integration tests for refactored managers using utilities
 */

// Import Node.js modules with proper type support
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs/promises';
import { expect, jest, describe, beforeAll, afterAll, beforeEach, afterEach, it } from '@jest/globals';

// Create a temporary test directory for integration tests
const INTEGRATION_TEST_DIR = path.join(os.tmpdir(), `integration-test-${Date.now()}`);
const TEST_CONFIG_DIR = path.join(INTEGRATION_TEST_DIR, '.confluence-upload');
const TEST_KEY_FILE = path.join(TEST_CONFIG_DIR, 'master.key');
const TEST_CREDENTIALS_FILE = path.join(TEST_CONFIG_DIR, 'credentials.enc');
const TEST_COOKIES_FILE = path.join(TEST_CONFIG_DIR, 'cookies.enc');

// Mock the config to use test directory
jest.mock('../../src/config/app-config', () => ({
  defaultConfig: {
    paths: {
      configDir: TEST_CONFIG_DIR,
      keyFile: TEST_KEY_FILE,
      credentialsFile: TEST_CREDENTIALS_FILE,
      cookiesFile: TEST_COOKIES_FILE
    },
    crypto: {
      keyLength: 32,
      pbkdf2Iterations: 100000,
      algorithm: 'aes-256-gcm'
    }
  }
}));

import { SecureCredentialsManager, ConfluenceCredential } from '../../src/services/secure-credentials-manager';
import { SecureCookieManager, EnhancedCookie } from '../../src/services/secure-cookie-manager';
import { CryptoUtils } from '../../src/utils/crypto-utils';
import { FileUtils } from '../../src/utils/file-utils';

// Setup and teardown
beforeAll(async () => {
  await fs.mkdir(INTEGRATION_TEST_DIR, { recursive: true });
});

afterAll(async () => {
  try {
    await fs.rm(INTEGRATION_TEST_DIR, { recursive: true, force: true });
  } catch (error) {
    console.error('Failed to clean up integration test directory:', error);
  }
});

describe('Integration Tests - Refactored Managers', () => {
  const testPassword = 'integration-test-password-123';
  const testCredential: ConfluenceCredential = {
    name: 'Test Confluence',
    baseUrl: 'https://test.atlassian.net',
    token: 'test-token-123',
    spaceKey: 'TEST',
    puppeteerLogin: false,
    username: 'testuser',
    password: 'testpass'
  };

  const testCookies = [
    {
      name: 'session_id',
      value: 'abc123def456',
      domain: '.test.atlassian.net',
      path: '/',
      expires: Date.now() + 86400000,
      httpOnly: true,
      secure: true,
      sameSite: 'Lax' as const
    },
    {
      name: 'csrf_token',
      value: 'xyz789uvw012',
      domain: 'test.atlassian.net',
      path: '/wiki',
      httpOnly: false,
      secure: true,
      sameSite: 'Strict' as const
    }
  ];

  beforeEach(async () => {
    // Clean up test files before each test
    try {
      await fs.rm(TEST_CONFIG_DIR, { recursive: true, force: true });
    } catch {
      // Directory might not exist
    }
    await fs.mkdir(TEST_CONFIG_DIR, { recursive: true });
  });

  describe('SecureCredentialsManager Integration', () => {
    describe('Initialization and Key Management', () => {
      it('should initialize with new encrypted key file', async () => {
        const manager = new SecureCredentialsManager();
        await manager.initialize(testPassword);

        // Verify key file was created and is encrypted
        const keyFileExists = await FileUtils.fileExists(TEST_KEY_FILE);
        expect(keyFileExists).toBe(true);

        const keyContent = await FileUtils.readFile(TEST_KEY_FILE);
        const keyData = JSON.parse(keyContent);
        expect(CryptoUtils.isEncryptedMasterKey(keyData)).toBe(true);

        await manager.cleanup();
      });

      it('should initialize with existing encrypted key file', async () => {
        // First, create an encrypted key file
        const manager1 = new SecureCredentialsManager();
        await manager1.initialize(testPassword);
        await manager1.cleanup();

        // Then initialize a new manager with the same password
        const manager2 = new SecureCredentialsManager();
        await manager2.initialize(testPassword);

        // Should work without errors
        const credentials = manager2.getCredentials();
        expect(Array.isArray(credentials)).toBe(true);

        await manager2.cleanup();
      });

      it('should fail with wrong password for encrypted key', async () => {
        // Create encrypted key file
        const manager1 = new SecureCredentialsManager();
        await manager1.initialize(testPassword);
        await manager1.cleanup();

        // Try to initialize with wrong password
        const manager2 = new SecureCredentialsManager();
        await expect(manager2.initialize('wrong-password'))
          .rejects.toThrow(/Invalid password/);
      });
    });

    describe('Credential Operations', () => {
      let manager: SecureCredentialsManager;

      beforeEach(async () => {
        manager = new SecureCredentialsManager();
        await manager.initialize(testPassword);
      });

      afterEach(async () => {
        await manager.cleanup();
      });

      it('should add and retrieve credentials', async () => {
        await manager.addOrUpdateCredential(testCredential);
        
        const credentials = manager.getCredentials();
        expect(credentials).toHaveLength(1);
        expect(credentials[0]).toEqual(testCredential);
      });

      it('should update existing credentials', async () => {
        await manager.addOrUpdateCredential(testCredential);
        
        const updatedCredential = {
          ...testCredential,
          token: 'updated-token-456'
        };
        await manager.addOrUpdateCredential(updatedCredential);
        
        const credentials = manager.getCredentials();
        expect(credentials).toHaveLength(1);
        expect(credentials[0].token).toBe('updated-token-456');
      });

      it('should retrieve credential by base URL', async () => {
        await manager.addOrUpdateCredential(testCredential);
        
        const found = manager.getCredentialByBaseUrl(testCredential.baseUrl);
        expect(found).toEqual(testCredential);
        
        const notFound = manager.getCredentialByBaseUrl('https://nonexistent.com');
        expect(notFound).toBeUndefined();
      });

      it('should remove credentials', async () => {
        await manager.addOrUpdateCredential(testCredential);
        
        const removed = await manager.removeCredential(testCredential.baseUrl);
        expect(removed).toBe(true);
        
        const credentials = manager.getCredentials();
        expect(credentials).toHaveLength(0);
        
        const removedAgain = await manager.removeCredential(testCredential.baseUrl);
        expect(removedAgain).toBe(false);
      });
    });
  });

  describe('SecureCookieManager Integration', () => {
    describe('Cookie Operations', () => {
      let credManager: SecureCredentialsManager;
      let cookieManager: SecureCookieManager;

      beforeEach(async () => {
        credManager = new SecureCredentialsManager();
        await credManager.initialize(testPassword);
        
        cookieManager = new SecureCookieManager();
        await cookieManager.initialize(testPassword);
      });

      afterEach(async () => {
        await credManager.cleanup();
        await cookieManager.cleanup();
      });

      it('should save and retrieve cookies', async () => {
        await cookieManager.saveCookies(testCookies, testCredential.baseUrl);
        
        const cookies = cookieManager.getCookies();
        expect(cookies.length).toBe(testCookies.length);
        
        cookies.forEach((cookie, index) => {
          expect(cookie.name).toBe(testCookies[index].name);
          expect(cookie.value).toBe(testCookies[index].value);
          expect(cookie.domain).toBe(testCookies[index].domain);
          expect(cookie.baseUrl).toBe(testCredential.baseUrl);
        });
      });

      it('should filter cookies by base URL', async () => {
        const baseUrl1 = 'https://test1.atlassian.net';
        const baseUrl2 = 'https://test2.atlassian.net';
        
        const cookies1 = [
          { ...testCookies[0], domain: '.test1.atlassian.net' }
        ];
        const cookies2 = [
          { ...testCookies[1], domain: '.test2.atlassian.net' }
        ];

        await cookieManager.saveCookies(cookies1, baseUrl1);
        await cookieManager.saveCookies(cookies2, baseUrl2);

        const filteredCookies1 = cookieManager.getCookiesForBaseUrl(baseUrl1);
        const filteredCookies2 = cookieManager.getCookiesForBaseUrl(baseUrl2);

        expect(filteredCookies1).toHaveLength(1);
        expect(filteredCookies2).toHaveLength(1);
        expect(filteredCookies1[0].domain).toBe('.test1.atlassian.net');
        expect(filteredCookies2[0].domain).toBe('.test2.atlassian.net');
      });
    });
  });
});