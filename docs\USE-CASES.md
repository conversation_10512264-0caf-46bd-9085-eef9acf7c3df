# Use Cases for Confluence Page Update Project

## Introduction

This document outlines the detailed use cases for the Confluence Page Update CLI tool. The project provides functionality to manage Confluence credentials securely, authenticate to Confluence instances, and update page content by converting Markdown files to HTML and uploading them via the Confluence API.

The system supports two authentication methods:
- API Token-based authentication
- Browser-based authentication using Puppeteer with cookie management

## System Overview

```mermaid
graph TD
    A[User] -->|Manages Credentials| B[UC-001: List Credentials]
    A -->|Manages Credentials| C[UC-002: Add/Update Credential]
    A -->|Manages Credentials| D[UC-003: Remove Credential]
    A -->|Manages Credentials| E[UC-004: Import Credentials]
    A -->|Manages Credentials| F[UC-005: Export Credentials]
    A -->|Updates Content| G[UC-008: Update Page Content]
    
    C -->|Stores Securely| H[Secure Credentials Manager]
    D -->|Stores Securely| H
    E -->|Stores Securely| H
    F -->|Reads from| H
    
    G -->|Authenticates via API| I[UC-006: API Token Auth]
    G -->|Authenticates via Browser| J[UC-007: Browser Auth]
    
    J -->|Manages Session| K[Secure Cookie Manager]
    
    H -->|Provides Credentials| I
    H -->|Provides Credentials| J
    K -->|Provides Cookies| J
    
    I -->|Calls| L[Confluence REST API]
    J -->|Calls| L
    
    G -->|Updates| L
    
    %% New utility components
    M[Crypto Utils] -->|Used by| H
    M -->|Used by| K
    N[File Utils] -->|Used by| H
    N -->|Used by| K
    O[Logger] -->|Used by| G
    O -->|Used by| H
    O -->|Used by| K
    P[Password Input] -->|Used by| H
    Q[Error Handling] -->|Used by| G
    Q -->|Used by| H
    Q -->|Used by| K
    R[Config] -->|Used by| G
    R -->|Used by| H
    R -->|Used by| K
    S[Lazy Puppeteer Manager] -->|Used by| J
```

## Detailed Use Cases

### UC-001: List All Credentials

**Actors:** User, Secure Credentials Manager

**Description:** User views all stored Confluence credentials to see available instances and their configuration.

**Preconditions:**
- Secure Credentials Manager is initialized with master password
- User has access to the credential manager CLI

**Steps:**
1. User executes credential manager CLI
2. System prompts for master password
3. User enters master password for authentication
4. User selects option "1. List all credentials"
5. System loads encrypted credentials from storage
6. System decrypts credentials using password-protected master key
7. System displays list of credentials with:
   - Index number
   - Name (e.g., "Noser Group", "SBB")
   - Base URL
   - Space Key
   - Authentication method (API Token or Browser Login)
   - Username (if applicable)

**Postconditions:**
- User sees all available credentials
- No data is modified

**Variations/Exceptions:**
- **V1:** No credentials stored - System displays "No credentials stored."
- **E1:** Invalid password - System displays error and exits
- **E2:** Decryption fails - System displays error and exits
- **E3:** Corrupted credentials file - System starts with empty credentials

---

### UC-002: Add or Update Credential

**Actors:** User, Secure Credentials Manager

**Description:** User adds a new Confluence credential or updates an existing one with authentication details.

**Preconditions:**
- Secure Credentials Manager is initialized with master password
- User has Confluence instance details (URL, space key, authentication method)

**Steps:**
1. User executes credential manager CLI
2. System prompts for master password
3. User enters master password for authentication
4. User selects option "2. Add/Update credential"
5. System prompts for credential details:
   - Name (e.g., "Company Confluence")
   - Base URL (e.g., "https://confluence.example.com")
   - Space Key
   - Authentication Method (1 for API Token, 2 for Browser Login)
6. If API Token selected:
   - System prompts for API Token
7. If Browser Login selected:
   - System prompts for Username (email)
   - System prompts for Password (optional)
8. System creates credential object
9. System encrypts and saves credentials to secure storage using password-protected master key
10. System confirms successful save

**Postconditions:**
- New credential is stored securely with encryption
- Existing credential is updated if same baseUrl exists
- Credentials are available for authentication

**Variations/Exceptions:**
- **V1:** Updating existing credential - System overwrites existing entry with same baseUrl
- **E1:** Invalid password - System displays error and exits
- **E2:** Invalid URL format - System displays error and re-prompts
- **E3:** Missing required fields - System displays error and re-prompts
- **E4:** Encryption/save fails - System displays error message

---

### UC-003: Remove Credential

**Actors:** User, Secure Credentials Manager

**Description:** User removes a stored Confluence credential from secure storage.

**Preconditions:**
- Secure Credentials Manager is initialized with master password
- At least one credential exists in storage

**Steps:**
1. User executes credential manager CLI
2. System prompts for master password
3. User enters master password for authentication
4. User selects option "3. Remove credential"
5. System displays numbered list of available credentials
6. User selects credential number to remove
7. System validates selection
8. System prompts for confirmation: "Are you sure you want to remove [name]? (y/n)"
9. If user confirms (y):
   - System removes credential from memory
   - System encrypts and saves updated credentials using password-protected master key
   - System confirms successful removal
10. If user cancels (n):
    - System displays "Operation cancelled"

**Postconditions:**
- Selected credential is permanently removed
- Updated credentials are saved to encrypted storage
- Remaining credentials are unaffected

**Variations/Exceptions:**
- **V1:** No credentials to remove - System displays "No credentials to remove."
- **E1:** Invalid password - System displays error and exits
- **E2:** Invalid selection - System displays "Invalid selection."
- **E3:** Save operation fails - System displays error message

---

### UC-004: Import Credentials from JSON

**Actors:** User, Secure Credentials Manager

**Description:** User imports multiple credentials from a plain JSON configuration file into secure storage.

**Preconditions:**
- Secure Credentials Manager is initialized with master password
- Valid JSON file exists with credential array
- JSON file follows expected format (array of credential objects)

**Steps:**
1. User executes credential manager CLI with optional `--import <file>` argument
2. System prompts for master password
3. User enters master password for authentication
4. If command-line import:
   - System uses provided file path
5. If interactive mode:
   - User selects option "4. Import from JSON file"
   - System prompts for file path (defaults to ./config.json)
   - User provides file path or accepts default
6. System validates file existence and extension
7. System reads and parses JSON file
8. System validates file format (must be array)
9. System validates each credential object for required fields:
   - name, baseUrl, spaceKey
10. System displays credential count and prompts for confirmation
11. If confirmed, system replaces current credentials with imported ones
12. System encrypts and saves credentials to secure storage using password-protected master key
13. System displays success message with count of imported credentials

**Postconditions:**
- All credentials from JSON file are stored securely with encryption
- Previous credentials are replaced
- Credentials are available for authentication

**Variations/Exceptions:**
- **V1:** Command-line import - System skips interactive prompts
- **E1:** Invalid password - System displays error and exits
- **E2:** File not found - System displays "Import failed: [error]"
- **E3:** Invalid JSON format - System displays parsing error
- **E4:** Invalid credential format - System displays validation error
- **E5:** Missing required fields - System displays field validation error
- **E6:** User cancels confirmation - System displays "Import cancelled"

---

### UC-005: Export Credentials to JSON

**Actors:** User, Secure Credentials Manager

**Description:** User exports stored credentials to a plain JSON file for backup or migration purposes.

**Preconditions:**
- Secure Credentials Manager is initialized with master password
- At least one credential exists in storage

**Steps:**
1. User executes credential manager CLI
2. System prompts for master password
3. User enters master password for authentication
4. User selects option "5. Export credentials to JSON"
5. System loads and decrypts stored credentials
6. System displays count of credentials to export
7. System prompts for export file path (defaults to ./credentials-export.json)
8. User provides file path or accepts default
9. System displays security warning about plain text export
10. System prompts for confirmation to proceed
11. If confirmed:
    - System creates plain JSON export of all credentials
    - System writes export file with secure permissions (0600)
    - System displays success message and security reminder
12. If cancelled:
    - System displays "Export cancelled"

**Postconditions:**
- Credentials are exported to plain JSON file
- Export file contains sensitive data in readable format
- Original encrypted credentials remain unchanged

**Variations/Exceptions:**
- **V1:** No credentials to export - System displays "No credentials to export"
- **E1:** Invalid password - System displays error and exits
- **E2:** File write error - System displays export failure message
- **E3:** User cancels confirmation - System displays "Export cancelled"

---

### UC-006: Authenticate to Confluence (API Token)

**Actors:** User, System, Confluence API

**Description:** System authenticates to Confluence using stored API token for subsequent API operations.

**Preconditions:**
- Valid credential exists with API token
- Confluence instance is accessible
- puppeteerLogin is set to false in credential

**Steps:**
1. System retrieves credential for specified baseUrl
2. System validates that API token exists
3. System prepares HTTP headers with Authorization: Bearer [token]
4. System makes test API call to validate token
5. System stores authentication headers for subsequent requests

**Postconditions:**
- System is authenticated to Confluence
- API token is validated and ready for use
- HTTP headers are prepared for API calls

**Variations/Exceptions:**
- **E1:** No API token found - System displays error and exits
- **E2:** Invalid/expired token - System displays authentication error
- **E3:** Network connectivity issues - System displays connection error
- **E4:** Confluence instance unavailable - System displays service error

---

### UC-007: Authenticate to Confluence (Browser Login with Puppeteer)

**Actors:** User, System, Puppeteer Browser, Confluence Web Interface

**Description:** System uses Puppeteer to automate browser login and obtain session cookies for API authentication.

**Preconditions:**
- Valid credential exists with puppeteerLogin set to true
- Puppeteer and Chrome/Chromium are available
- Confluence instance is accessible

**Steps:**
1. System checks for existing valid cookies
2. If no valid cookies or FORCE_CONFLUENCE_LOGIN=true:
   - System launches Puppeteer browser (non-headless)
   - System navigates to Confluence baseUrl
   - System displays message: "Please login manually in browser"
   - System waits for successful login detection
3. System validates login using multiple methods:
   - API validation via /rest/api/user/current endpoint
   - UI validation using login indicator selectors
4. System extracts all cookies from browser session
5. System saves cookies with metadata (domain, expiry, savedAt)
6. System closes browser
7. System prepares cookie headers for API requests

**Postconditions:**
- User is authenticated via browser session
- Session cookies are saved for reuse
- System can make authenticated API calls using cookies

**Variations/Exceptions:**
- **V1:** Valid cookies exist - System skips browser launch and uses existing cookies
- **E1:** Login timeout (3 minutes) - System displays timeout error
- **E2:** Browser launch fails - System displays Puppeteer error
- **E3:** Login validation fails - System prompts for retry
- **E4:** Cookie save fails - System displays storage error

---

### UC-007: Load and Manage Session Cookies (Encrypted)

**Actors:** System, Secure Cookie Manager

**Description:** System loads stored session cookies from encrypted centralized location and uses 401-retry logic for authentication validation.

**Preconditions:**
- Encrypted cookie storage file exists in centralized location (`$USERHOME/.confluence-uploader/cookies.enc`)
- Cookies were previously saved from browser session
- Master key is available for decryption

**Steps:**
1. System initializes Secure Cookie Manager with master key
2. System loads cookies from encrypted storage file (`~/.confluence-uploader/cookies.enc`)
3. System decrypts cookies using password-protected master key
4. System validates cookie format (supports both legacy and encrypted formats)
5. System filters cookies for target domain (matches target Confluence instance)
6. System prepares cookie string for HTTP headers without validation
7. System proceeds with API operations using cookies
8. If 401 error occurs during API calls:
   - System deletes invalid encrypted cookie file
   - System triggers new authentication flow (UC-007)
   - System retries the failed API call with new cookies

**Postconditions:**
- Encrypted cookies are loaded and used for API requests
- Invalid cookies are automatically handled via 401-retry logic
- Authentication state is maintained through automatic re-authentication
- All cookie operations use encryption

**Variations/Exceptions:**
- **V1:** Legacy cookie format - System migrates to encrypted format automatically
- **V2:** Encrypted cookie format - System handles encrypted format with metadata
- **V3:** No cookies exist - System triggers initial authentication (UC-007)
- **E1:** Decryption fails - System requires re-authentication
- **E2:** Corrupted cookie file - System deletes file and requires re-authentication
- **E3:** No cookies for domain - System requires new authentication
- **E4:** 401 during API call - System automatically re-authenticates and retries

---

### UC-008: Update Confluence Page Content

**Actors:** User, System, Confluence API, Secure Credentials Manager

**Description:** User updates a Confluence page by converting Markdown content to HTML and uploading via API with secure authentication.

**Preconditions:**
- User is authenticated (via API token or encrypted browser session)
- Valid Confluence page ID exists
- Markdown file exists and is readable
- User has edit permissions on the target page
- Credentials are stored in encrypted format

**Steps:**
1. User executes main script with parameters:
   - Confluence Page ID (command line or prompt)
   - Markdown file path (command line or prompt)
   - Confluence instance name (e.g., "SBB", "NOSER GROUP") or base URL (command line or selected from stored credentials)
2. System initializes Secure Credentials Manager and loads encrypted credentials
3. System validates inputs:
   - Page ID is not empty
   - Markdown file exists
4. System reads Markdown file content
5. System converts Markdown to HTML using marked library:
   - Custom code block renderer for Confluence macros
   - XHTML compliance for Confluence storage format
6. System determines authentication method from stored credentials:
   - If API Token: Uses Bearer token authentication
   - If Browser Login: Loads encrypted cookies via Secure Cookie Manager
7. System fetches current page data with 401-retry logic:
   - GET /rest/api/content/{pageId}?expand=body.storage,version
   - Extracts current version number and title
   - If 401 error and browser login: Triggers re-authentication and retry
8. System prepares update payload:
   - Increments version number
   - Preserves existing title
   - Sets new HTML content in storage format
9. System updates page with 401-retry logic:
   - PUT /rest/api/content/{pageId}
   - Uses authentication headers (token or encrypted cookies)
   - If 401 error and browser login: Triggers re-authentication and retry
10. System validates update response
11. System displays success message with page link

**Postconditions:**
- Confluence page content is updated with converted Markdown
- Page version is incremented
- User receives confirmation with page link
- Authentication state is maintained securely

**Variations/Exceptions:**
- **V1:** Multiple Confluence instances - User selects from available encrypted credentials
- **V2:** Command line parameters - System uses provided values without prompts
- **V3:** Interactive mode - System prompts for missing parameters
- **V4:** 401 Authentication error - System automatically re-authenticates and retries
- **E1:** Credentials not found - System displays error and suggests using credential manager
- **E2:** Page not found - System displays 404 error
- **E3:** Insufficient permissions - System displays authorization error
- **E4:** Invalid Markdown - System displays conversion error
- **E5:** Network/API errors - System displays detailed error message
- **E6:** Version conflict - System displays concurrent edit error
- **E7:** Cookie decryption fails - System triggers new authentication

## Technical Notes

### Authentication Flow Decision Matrix

| Credential Type | puppeteerLogin | Authentication Method |
|----------------|----------------|----------------------|
| API Token      | false          | Bearer token in Authorization header |
| Browser Login  | true           | Encrypted session cookies in Cookie header |

### Cookie Management Features

- **Encrypted Storage**: All cookies stored encrypted in `$USERHOME/.confluence-uploader/cookies.enc`
- **Master Key Protection**: Cookies encrypted using same password-protected master key as credentials
- **No Validation**: Cookies are used without expiry or age validation (relies on 401-retry logic)
- **401-Retry Logic**: Invalid cookies automatically trigger re-authentication and retry
- **Domain Filtering**: Only cookies matching the target Confluence domain are used
- **Format Compatibility**: Supports legacy plain format with automatic migration to encrypted format
- **Automatic Directory Creation**: Cookie directory is created automatically if needed
- **Secure Migration**: Legacy plain cookies are automatically encrypted and backed up on first use

### Security Enhancements

- **Password-Based Encryption**: Master key encrypted with PBKDF2 (100,000 iterations)
- **AES-256-GCM Encryption**: All sensitive data (credentials and cookies) use authenticated encryption
- **Automatic Migration**: Legacy plain files are automatically migrated to encrypted format
- **Secure File Permissions**: All sensitive files created with 0600 permissions (user-only access)

### Code Block Conversion

The system includes custom Markdown rendering for Confluence code blocks:
- Converts fenced code blocks (```) to Confluence code macros
- Preserves syntax highlighting language specification
- Handles CDATA escaping for special characters
- Supports empty code blocks

### Error Handling Strategy

- **401-Retry Logic**: Invalid cookies automatically trigger re-authentication and API retry
- **Graceful Degradation**: Authentication failures are handled transparently
- **User Guidance**: Clear error messages with suggested actions
- **Centralized Cookie Management**: All scripts use the same cookie location for consistency
- **Logging**: Detailed console output for debugging and user feedback