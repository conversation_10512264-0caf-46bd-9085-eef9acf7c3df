/**
 * Tests for Error Handler utility
 */

import { withErrorHandling, withErrorHandlingSync, ErrorHandlingOptions } from '../../src/utils/error-handler';
import { AppError, ValidationError, NetworkError, AuthenticationError, ApplicationError } from '../../src/errors';

// Mock console and process
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();
const mockConsoleDebug = jest.spyOn(console, 'debug').mockImplementation();
const mockProcessExit = jest.spyOn(process, 'exit').mockImplementation();

describe('Error Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    mockConsoleError.mockRestore();
    mockConsoleDebug.mockRestore();
    mockProcessExit.mockRestore();
  });

  describe('withErrorHandling', () => {
    it('should execute operation successfully', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      const options: ErrorHandlingOptions = {
        context: 'test operation',
        exitOnError: false
      };

      const result = await withErrorHandling(operation, options);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
      expect(mockConsoleError).not.toHaveBeenCalled();
    });

    it('should handle AppError with user message', async () => {
      const error = new ValidationError('Internal validation error', 'Please check your input');
      const operation = jest.fn().mockRejectedValue(error);
      const options: ErrorHandlingOptions = {
        context: 'validation',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockConsoleError).toHaveBeenCalledWith('Please check your input');
      expect(mockConsoleDebug).toHaveBeenCalledWith('Error in validation:', error);
    });

    it('should handle generic Error', async () => {
      const error = new Error('Generic error message');
      const operation = jest.fn().mockRejectedValue(error);
      const options: ErrorHandlingOptions = {
        context: 'generic operation',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);

      expect(mockConsoleError).toHaveBeenCalledWith('An unexpected error occurred. Please check the logs for more details. (generic operation)');
      expect(mockConsoleDebug).toHaveBeenCalledWith('Error in generic operation:', expect.any(Error));
    });

    it('should handle string errors', async () => {
      const operation = jest.fn().mockRejectedValue('String error');
      const options: ErrorHandlingOptions = {
        context: 'string error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);

      expect(mockConsoleError).toHaveBeenCalledWith('An unexpected error occurred. Please check the logs for more details. (string error test)');
    });

    it('should handle unknown error types', async () => {
      const operation = jest.fn().mockRejectedValue({ unknown: 'object' });
      const options: ErrorHandlingOptions = {
        context: 'unknown error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);

      expect(mockConsoleError).toHaveBeenCalledWith('An unexpected error occurred. Please check the logs for more details. (unknown error test)');
    });

    it('should use custom error handler', async () => {
      const error = new ValidationError('Test error', 'User message');
      const operation = jest.fn().mockRejectedValue(error);
      const customHandler = jest.fn().mockReturnValue('custom result');
      const options: ErrorHandlingOptions = {
        context: 'custom handler test',
        exitOnError: false,
        onError: customHandler
      };

      const result = await withErrorHandling(operation, options);

      expect(result).toBe('custom result');
      expect(customHandler).toHaveBeenCalledWith(error);
    });

    it('should use custom logger', async () => {
      const error = new NetworkError('Network failed', 'Connection error');
      const operation = jest.fn().mockRejectedValue(error);
      const mockLogger = {
        error: jest.fn(),
        debug: jest.fn()
      };
      const options: ErrorHandlingOptions = {
        context: 'custom logger test',
        exitOnError: false,
        logger: mockLogger
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith('Connection error');
      expect(mockLogger.debug).toHaveBeenCalledWith('Error in custom logger test:', error);
    });

    it('should handle silent mode', async () => {
      const error = new AuthenticationError('Auth failed', 'Login error');
      const operation = jest.fn().mockRejectedValue(error);
      const options: ErrorHandlingOptions = {
        context: 'silent test',
        exitOnError: false,
        silent: true
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockConsoleError).not.toHaveBeenCalled();
      expect(mockConsoleDebug).not.toHaveBeenCalled();
    });



    it('should handle logger without debug method', async () => {
      const error = new ValidationError('Test error', 'User message');
      const operation = jest.fn().mockRejectedValue(error);
      const mockLogger = {
        error: jest.fn()
        // No debug method
      };
      const options: ErrorHandlingOptions = {
        context: 'logger without debug',
        exitOnError: false,
        logger: mockLogger
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith('User message');
      // Should not crash when debug method is missing
    });

    it('should handle non-Error objects', async () => {
      const operation = jest.fn().mockRejectedValue('string error');
      const options: ErrorHandlingOptions = {
        context: 'string error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);
    });

    it('should handle null/undefined errors', async () => {
      const operation = jest.fn().mockRejectedValue(null);
      const options: ErrorHandlingOptions = {
        context: 'null error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);
    });

    it('should handle custom error handler returning value', async () => {
      const error = new ValidationError('Test error', 'User message');
      const operation = jest.fn().mockRejectedValue(error);
      const customHandler = jest.fn().mockReturnValue('handled');
      const options: ErrorHandlingOptions = {
        context: 'custom handler test',
        exitOnError: false,
        onError: customHandler
      };

      const result = await withErrorHandling(operation, options);

      expect(result).toBe('handled');
      expect(customHandler).toHaveBeenCalledWith(error);
    });
  });

  describe('withErrorHandlingSync', () => {
    it('should handle successful sync operations', () => {
      const operation = jest.fn().mockReturnValue('success');
      const options: ErrorHandlingOptions = {
        context: 'sync success test',
        exitOnError: false
      };

      const result = withErrorHandlingSync(operation, options);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
    });

    it('should handle AppError in sync operations', () => {
      const error = new ValidationError('Sync error', 'Sync user message');
      const operation = jest.fn().mockImplementation(() => { throw error; });
      const options: ErrorHandlingOptions = {
        context: 'sync validation test',
        exitOnError: false
      };

      expect(() => withErrorHandlingSync(operation, options)).toThrow(error);
      expect(mockConsoleError).toHaveBeenCalledWith('Sync user message');
    });

    it('should handle generic errors in sync operations', () => {
      const error = new Error('Sync generic error');
      const operation = jest.fn().mockImplementation(() => { throw error; });
      const options: ErrorHandlingOptions = {
        context: 'sync generic test',
        exitOnError: false
      };

      expect(() => withErrorHandlingSync(operation, options)).toThrow(ApplicationError);
    });

    it('should handle custom error handler in sync operations', () => {
      const error = new ValidationError('Sync error', 'Sync user message');
      const operation = jest.fn().mockImplementation(() => { throw error; });
      const customHandler = jest.fn().mockReturnValue('sync handled');
      const options: ErrorHandlingOptions = {
        context: 'sync custom handler test',
        exitOnError: false,
        onError: customHandler
      };

      const result = withErrorHandlingSync(operation, options);

      expect(result).toBe('sync handled');
      expect(customHandler).toHaveBeenCalledWith(error);
    });

    it('should handle logger without debug method in sync operations', () => {
      const error = new ValidationError('Sync error', 'Sync user message');
      const operation = jest.fn().mockImplementation(() => { throw error; });
      const mockLogger = {
        error: jest.fn()
        // No debug method
      };
      const options: ErrorHandlingOptions = {
        context: 'sync logger test',
        exitOnError: false,
        logger: mockLogger as any
      };

      expect(() => withErrorHandlingSync(operation, options)).toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith('Sync user message');
    });

    it('should handle non-Error objects in sync operations', () => {
      const operation = jest.fn().mockImplementation(() => { throw 'sync string error'; });
      const options: ErrorHandlingOptions = {
        context: 'sync string error test',
        exitOnError: false
      };

      expect(() => withErrorHandlingSync(operation, options)).toThrow(ApplicationError);
    });

    it('should handle null/undefined errors in sync operations', () => {
      const operation = jest.fn().mockImplementation(() => { throw null; });
      const options: ErrorHandlingOptions = {
        context: 'sync null error test',
        exitOnError: false
      };

      expect(() => withErrorHandlingSync(operation, options)).toThrow(ApplicationError);
    });
  });

  describe('Edge Cases', () => {
    it('should handle operations that throw non-standard objects', async () => {
      const weirdError = { message: 'weird error', code: 42 };
      const operation = jest.fn().mockRejectedValue(weirdError);
      const options: ErrorHandlingOptions = {
        context: 'weird error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);
    });

    it('should handle very long context strings', async () => {
      const error = new ValidationError('Test error', 'User message');
      const operation = jest.fn().mockRejectedValue(error);
      const longContext = 'a'.repeat(1000);
      const options: ErrorHandlingOptions = {
        context: longContext,
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);
    });

    it('should handle circular reference errors', async () => {
      const circularError: any = new Error('Circular error');
      circularError.self = circularError;
      const operation = jest.fn().mockRejectedValue(circularError);
      const options: ErrorHandlingOptions = {
        context: 'circular error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);
    });
  });
});
