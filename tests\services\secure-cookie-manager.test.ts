/**
 * Tests for SecureCookieManager
 */

import { SecureCookieManager } from '../../src/services/secure-cookie-manager';

// Mock dependencies
jest.mock('../../src/utils/crypto-utils');
jest.mock('../../src/utils/file-utils');
jest.mock('../../src/utils/logger');
jest.mock('../../src/config/app-config');

describe('SecureCookieManager', () => {
  let cookieManager: SecureCookieManager;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    cookieManager = new SecureCookieManager();
  });

  afterEach(async () => {
    // Cleanup
    try {
      await cookieManager.cleanup();
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Constructor', () => {
    it('should create instance', () => {
      expect(cookieManager).toBeInstanceOf(SecureCookieManager);
    });
  });

  describe('Basic Operations', () => {
    it('should start with empty cookies', () => {
      const cookies = cookieManager.getCookies();
      expect(cookies).toEqual([]);
    });

    it('should handle cleanup', async () => {
      await expect(cookieManager.cleanup()).resolves.not.toThrow();
    });

    it('should return false for non-existent cookies', () => {
      expect(cookieManager.hasCookiesForBaseUrl('https://example.com')).toBe(false);
    });

    it('should return empty stats initially', () => {
      const stats = cookieManager.getCookieStats();
      expect(stats.total).toBe(0);
      expect(stats.byDomain).toEqual({});
      expect(stats.encrypted).toBe(true);
    });

    it('should handle invalid URLs gracefully', () => {
      const cookies = cookieManager.getCookiesForBaseUrl('invalid-url');
      expect(cookies).toHaveLength(0);
    });
  });
});
