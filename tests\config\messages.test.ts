/**
 * Tests for Messages configuration
 */

import { messages } from '../../src/config/messages';

describe('Messages Configuration', () => {
  describe('Error Messages', () => {
    it('should have authentication error messages', () => {
      expect(messages.errors.authenticationFailed).toBe('Authentication failed');
      expect(messages.errors.invalidPassword).toBe('Invalid password or corrupted key file');
      expect(messages.errors.passwordRequired).toBe('Password required to decrypt master key');
    });

    it('should have network error messages', () => {
      expect(messages.errors.networkError).toBe('Network request failed');
    });

    it('should have file operation error messages', () => {
      expect(messages.errors.fileOperationFailed).toBe('File operation failed');
    });

    it('should have validation error messages', () => {
      expect(messages.errors.missingRequiredFields).toBe('Invalid credential: missing required fields');
      expect(messages.errors.invalidCredentialFormat).toBe('Invalid credentials format: expected an array');
    });

    it('should have crypto error messages', () => {
      expect(messages.errors.encryptionKeyNotInitialized).toBe('Encryption key not initialized');
      expect(messages.errors.keyFileNotFound).toBe('Master key not found. Please initialize credentials first using SecureCredentialsManager.');
      expect(messages.errors.corruptedData).toBe('Data appears to be corrupted');
    });
  });

  describe('Info Messages', () => {
    it('should have credential management info messages', () => {
      const importMessage = messages.info.credentialsImported(5);
      expect(importMessage).toBe('Successfully imported 5 credentials');

      const exportMessage = messages.info.credentialsExported(3, '/path/to/file.json');
      expect(exportMessage).toBe('Successfully exported 3 credentials to /path/to/file.json');
    });

    it('should handle zero credentials', () => {
      const importMessage = messages.info.credentialsImported(0);
      expect(importMessage).toBe('Successfully imported 0 credentials');

      const exportMessage = messages.info.credentialsExported(0, '/empty.json');
      expect(exportMessage).toBe('Successfully exported 0 credentials to /empty.json');
    });

    it('should handle large numbers of credentials', () => {
      const importMessage = messages.info.credentialsImported(1000);
      expect(importMessage).toBe('Successfully imported 1000 credentials');

      const exportMessage = messages.info.credentialsExported(999, '/large-file.json');
      expect(exportMessage).toBe('Successfully exported 999 credentials to /large-file.json');
    });

    it('should have cookie management info messages', () => {
      const cookieMessage = messages.info.cookiesSaved(10, 'example.com');
      expect(cookieMessage).toBe('10 cookies encrypted and saved for example.com');

      expect(messages.info.cookiesCleared).toBe('All cookies cleared');
    });

    it('should handle different cookie counts and domains', () => {
      const singleCookie = messages.info.cookiesSaved(1, 'test.com');
      expect(singleCookie).toBe('1 cookies encrypted and saved for test.com');

      const manyCookies = messages.info.cookiesSaved(50, 'subdomain.example.org');
      expect(manyCookies).toBe('50 cookies encrypted and saved for subdomain.example.org');
    });

    it('should have master key info messages', () => {
      expect(messages.info.masterKeyEncrypted).toBe('Master key has been encrypted and migrated to new format.');
      expect(messages.info.masterKeyMigrated).toBe('Master key migrated to encrypted format');
      expect(messages.info.initializationComplete).toBe('Secure manager initialized successfully');
    });
  });

  describe('Warning Messages', () => {
    it('should have security warning messages', () => {
      expect(messages.warnings.plainKeyFormat).toBe('Warning: Master key is stored in plain format. Consider providing a password to encrypt it.');
      expect(messages.warnings.sensitiveDataExported).toBe('Warning: The exported file contains sensitive information in plain text. Please store it securely and delete it after use.');
      expect(messages.warnings.legacyFormat).toBe('Warning: Using legacy format. Consider migrating to encrypted format.');
    });
  });

  describe('Message Structure', () => {
    it('should have all required message categories', () => {
      expect(messages).toHaveProperty('errors');
      expect(messages).toHaveProperty('warnings');
      expect(messages).toHaveProperty('info');
    });

    it('should have consistent message types', () => {
      // All error messages should be strings
      Object.values(messages.errors).forEach(message => {
        expect(typeof message).toBe('string');
      });

      // All warning messages should be strings
      Object.values(messages.warnings).forEach(message => {
        expect(typeof message).toBe('string');
      });

      // Info messages can be strings or functions
      Object.values(messages.info).forEach(message => {
        expect(['string', 'function']).toContain(typeof message);
      });
    });

    it('should have non-empty messages', () => {
      // Check that all string messages are non-empty
      Object.values(messages.errors).forEach(message => {
        expect(message.length).toBeGreaterThan(0);
      });

      Object.values(messages.warnings).forEach(message => {
        expect(message.length).toBeGreaterThan(0);
      });

      // Check function messages return non-empty strings
      const credImported = messages.info.credentialsImported(1);
      expect(credImported.length).toBeGreaterThan(0);

      const credExported = messages.info.credentialsExported(1, 'test.json');
      expect(credExported.length).toBeGreaterThan(0);

      const cookiesSaved = messages.info.cookiesSaved(1, 'test.com');
      expect(cookiesSaved.length).toBeGreaterThan(0);
    });
  });
});
