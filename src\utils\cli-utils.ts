/**
 * CLI Utilities for consistent command-line interface handling
 */

export interface CliOption {
  short: string;
  long: string;
  description: string;
  hasValue?: boolean;
  required?: boolean;
}

export interface CliCommand {
  name: string;
  description: string;
  usage: string;
  options: CliOption[];
  examples: string[];
  version?: string;
}

export interface ParsedArgs {
  flags: Record<string, boolean>;
  values: Record<string, string>;
  positional: string[];
  help: boolean;
  version: boolean;
}

export class CliParser {
  private command: CliCommand;

  constructor(command: CliCommand) {
    this.command = command;
  }

  /**
   * Parse command line arguments
   */
  parse(args: string[] = process.argv.slice(2)): ParsedArgs {
    const result: ParsedArgs = {
      flags: {},
      values: {},
      positional: [],
      help: false,
      version: false
    };

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];

      // Handle help flags
      if (arg === '--help' || arg === '-h') {
        result.help = true;
        continue;
      }

      // Handle version flags
      if (arg === '--version' || arg === '-V') {
        result.version = true;
        continue;
      }

      // Handle long options
      if (arg.startsWith('--')) {
        const optionName = arg.substring(2);
        const option = this.command.options.find(opt => opt.long === optionName);
        
        if (option) {
          if (option.hasValue) {
            if (i + 1 < args.length && !args[i + 1].startsWith('-')) {
              result.values[option.long] = args[i + 1];
              i++; // Skip next argument as it's the value
            } else {
              throw new Error(`Option --${option.long} requires a value`);
            }
          } else {
            result.flags[option.long] = true;
          }
        } else {
          throw new Error(`Unknown option: ${arg}`);
        }
        continue;
      }

      // Handle short options
      if (arg.startsWith('-') && arg.length > 1) {
        const shortOptions = arg.substring(1);
        
        for (let j = 0; j < shortOptions.length; j++) {
          const shortOpt = shortOptions[j];
          const option = this.command.options.find(opt => opt.short === shortOpt);
          
          if (option) {
            if (option.hasValue) {
              // For short options with values, the value can be:
              // 1. The rest of the current argument (-ifile.txt)
              // 2. The next argument (-i file.txt)
              if (j < shortOptions.length - 1) {
                result.values[option.long] = shortOptions.substring(j + 1);
                break;
              } else if (i + 1 < args.length && !args[i + 1].startsWith('-')) {
                result.values[option.long] = args[i + 1];
                i++; // Skip next argument as it's the value
                break;
              } else {
                throw new Error(`Option -${option.short} requires a value`);
              }
            } else {
              result.flags[option.long] = true;
            }
          } else {
            throw new Error(`Unknown option: -${shortOpt}`);
          }
        }
        continue;
      }

      // Positional argument
      result.positional.push(arg);
    }

    return result;
  }

  /**
   * Show help text
   */
  showHelp(): void {
    console.log(`${this.command.name} - ${this.command.description}`);
    console.log('');
    console.log(`Usage: ${this.command.usage}`);
    console.log('');
    
    if (this.command.options.length > 0) {
      console.log('Options:');
      
      // Calculate padding for alignment
      const maxOptionLength = Math.max(
        ...this.command.options.map(opt => 
          `  -${opt.short}, --${opt.long}${opt.hasValue ? ' <value>' : ''}`.length
        )
      );
      
      this.command.options.forEach(option => {
        const optionText = `  -${option.short}, --${option.long}${option.hasValue ? ' <value>' : ''}`;
        const padding = ' '.repeat(Math.max(2, maxOptionLength - optionText.length + 2));
        console.log(`${optionText}${padding}${option.description}`);
      });
      console.log('');
    }
    
    if (this.command.examples.length > 0) {
      console.log('Examples:');
      this.command.examples.forEach(example => {
        console.log(`  ${example}`);
      });
      console.log('');
    }
  }

  /**
   * Show version information
   */
  showVersion(): void {
    if (this.command.version) {
      console.log(`${this.command.name} v${this.command.version}`);
    } else {
      console.log(this.command.name);
    }
  }

  /**
   * Validate required options
   */
  validateRequired(parsed: ParsedArgs): void {
    const missingRequired = this.command.options
      .filter(opt => opt.required)
      .filter(opt => !(opt.long in parsed.values) && !(opt.long in parsed.flags))
      .map(opt => `--${opt.long}`);

    if (missingRequired.length > 0) {
      throw new Error(`Missing required options: ${missingRequired.join(', ')}`);
    }
  }
}

/**
 * Standard CLI options used across tools
 */
export const STANDARD_OPTIONS: CliOption[] = [
  {
    short: 'h',
    long: 'help',
    description: 'Show help information'
  },
  {
    short: 'V',
    long: 'version',
    description: 'Show version information'
  },
  {
    short: 'v',
    long: 'verbose',
    description: 'Show detailed debug information'
  },
  {
    short: 'q',
    long: 'quiet',
    description: 'Show only errors (suppress info messages)'
  }
];

/**
 * Handle standard CLI flow (help, version, error handling)
 */
export function handleStandardCliFlow(
  parser: CliParser,
  parsed: ParsedArgs,
  onError?: (error: Error) => void
): boolean {
  try {
    if (parsed.help) {
      parser.showHelp();
      return true; // Exit after showing help
    }

    if (parsed.version) {
      parser.showVersion();
      return true; // Exit after showing version
    }

    parser.validateRequired(parsed);
    return false; // Continue with normal execution
  } catch (error) {
    if (onError) {
      onError(error as Error);
    } else {
      console.error(`Error: ${(error as Error).message}`);
      console.error('Use --help for usage information.');
      process.exit(1);
    }
    return true; // Exit after error
  }
}

/**
 * Standard exit codes
 */
export const EXIT_CODES = {
  SUCCESS: 0,
  GENERAL_ERROR: 1,
  SYSTEM_ERROR: 2,
  APPLICATION_ERROR: 3,
  USER_CANCELLED: 130 // Ctrl+C
} as const;

/**
 * Graceful exit with cleanup
 */
export function gracefulExit(
  code: number = EXIT_CODES.SUCCESS,
  message?: string,
  cleanup?: () => void
): void {
  if (message) {
    if (code === EXIT_CODES.SUCCESS) {
      console.log(message);
    } else {
      console.error(message);
    }
  }
  
  if (cleanup) {
    try {
      cleanup();
    } catch (error) {
      console.error('Error during cleanup:', (error as Error).message);
    }
  }
  
  process.exit(code);
}